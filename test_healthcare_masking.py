#!/usr/bin/env python3
"""
Test script specifically for healthcare data masking functionality
"""

import pandas as pd
from datetime import datetime
from masking_engine import mask_column

def test_healthcare_masking():
    """Test healthcare-specific masking methods"""
    print("🏥 Testing Healthcare-Specific Masking Methods")
    print("=" * 60)
    
    # Sample healthcare data
    test_data = {
        'patient_names': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'medical_record_numbers': ['MRN123456', 'MRN789012', 'MRN345678'],
        'birth_dates': ['1985-03-15', '1992-07-22', '1978-11-08'],
        'clinical_notes': ['Patient presents with chest pain and shortness of breath', 
                          'Follow-up visit for diabetes management',
                          'Routine physical examination completed'],
        'vital_signs': [120, 98.6, 72, 16, 98],  # BP, temp, HR, RR, O2sat
        'lab_results': [85.5, 180.2, 4.2, 12.8, 7.1],
        'insurance_numbers': ['ABC123456789', 'DEF987654321', 'GHI456789012']
    }
    
    # Test healthcare-specific methods
    healthcare_methods = [
        ('generate_mrn', 'Medical Record Numbers'),
        ('generate_patient_id', 'Patient IDs'),
        ('generate_insurance_number', 'Insurance Numbers'),
        ('mask_clinical_notes', 'Clinical Notes'),
        ('realistic_vital_signs', 'Vital Signs'),
        ('mask_lab_results', 'Lab Results'),
        ('hipaa_age_group', 'Birth Dates → Age Groups')
    ]
    
    for method, description in healthcare_methods:
        print(f"\n🔹 {description} ({method}):")
        
        if method == 'hipaa_age_group':
            test_values = test_data['birth_dates']
        elif method == 'mask_clinical_notes':
            test_values = test_data['clinical_notes']
        elif method in ['realistic_vital_signs', 'mask_lab_results']:
            test_values = test_data['vital_signs'][:3]
        else:
            test_values = test_data['medical_record_numbers']
        
        for val in test_values:
            try:
                result = mask_column(val, method)
                print(f"  '{val}' → '{result}'")
            except Exception as e:
                print(f"  ERROR with '{val}': {e}")
    
    # Test standard methods with healthcare data
    print(f"\n🔹 Standard Methods with Healthcare Data:")
    
    standard_methods = [
        ('faker.name()', test_data['patient_names'], 'Patient Names'),
        ('hash_sha256', test_data['insurance_numbers'], 'Insurance Numbers (Hashed)'),
        ('shift_days_random', test_data['birth_dates'], 'Birth Dates (Shifted)'),
        ('add_noise_percentage', test_data['lab_results'], 'Lab Results (With Noise)')
    ]
    
    for method, test_values, description in standard_methods:
        print(f"\n  {description} ({method}):")
        for val in test_values[:2]:  # Test first 2 values
            try:
                result = mask_column(val, method)
                print(f"    '{val}' → '{result}'")
            except Exception as e:
                print(f"    ERROR with '{val}': {e}")

def test_hipaa_compliance():
    """Test HIPAA Safe Harbor compliance"""
    print(f"\n🔒 HIPAA Safe Harbor Compliance Test")
    print("=" * 40)
    
    # HIPAA identifiers and recommended masking
    hipaa_identifiers = {
        'Names': ('John Doe', 'faker.name()'),
        'Geographic subdivisions': ('123 Main St, Boston, MA', 'faker.address()'),
        'Dates': ('1985-03-15', 'hipaa_age_group'),
        'Phone numbers': ('************', 'faker.phone_number()'),
        'Email addresses': ('<EMAIL>', 'faker.email()'),
        'Social security numbers': ('***********', 'hash_sha256'),
        'Medical record numbers': ('MRN123456', 'generate_mrn'),
        'Account numbers': ('ACC789012', 'hash_sha256')
    }
    
    for identifier_type, (sample_value, method) in hipaa_identifiers.items():
        try:
            result = mask_column(sample_value, method)
            print(f"✅ {identifier_type}: '{sample_value}' → '{result}'")
        except Exception as e:
            print(f"❌ {identifier_type}: ERROR - {e}")

def test_medical_data_integrity():
    """Test that medical data maintains clinical relevance"""
    print(f"\n📊 Medical Data Integrity Test")
    print("=" * 35)
    
    # Test vital signs
    vital_signs = {
        'Systolic BP': 120,
        'Heart Rate': 72,
        'Temperature': 98.6,
        'Respiratory Rate': 16,
        'O2 Saturation': 98
    }
    
    print("🩺 Vital Signs (should remain clinically realistic):")
    for vital_name, value in vital_signs.items():
        masked_value = mask_column(value, 'realistic_vital_signs')
        print(f"  {vital_name}: {value} → {masked_value}")
    
    # Test lab results
    lab_results = {
        'Glucose': 85.5,
        'Cholesterol': 180.2,
        'Hemoglobin': 12.8,
        'White Blood Cells': 7.1
    }
    
    print("\n🧪 Lab Results (with clinical noise):")
    for lab_name, value in lab_results.items():
        masked_value = mask_column(value, 'mask_lab_results')
        print(f"  {lab_name}: {value} → {masked_value}")

def main():
    """Run all healthcare masking tests"""
    try:
        test_healthcare_masking()
        test_hipaa_compliance()
        test_medical_data_integrity()
        
        print("\n" + "=" * 60)
        print("✅ All healthcare masking tests completed!")
        print("🏥 Your tool is ready for hospital management systems!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
