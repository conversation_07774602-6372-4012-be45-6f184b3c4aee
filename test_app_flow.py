#!/usr/bin/env python3
"""
Test the app flow to verify generation buttons appear
"""

def test_app_structure():
    """Test that all key sections exist in the app"""
    print("🧪 Testing App Structure and Flow")
    print("=" * 40)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        with open('app.py', 'r', encoding='latin-1') as f:
            content = f.read()
    
    # Key sections that should exist
    sections = [
        ("🎯 Choose Masking Mode", "Mode selection"),
        ("📦 Select a table", "Table selection"),
        ("🤖 Use AI-Powered Suggestions", "AI mode checkbox"),
        ("🤖 Generate AI Suggestions", "AI generation button"),
        ("⚡ Apply Healthcare Rules", "Manual generation button"),
        ("⚙️ Customize Your Masking", "Masking configuration"),
        ("🎭 Apply Customized Masking", "Apply masking button"),
        ("🎭 Masked Data Preview", "Results display")
    ]
    
    print("📋 Checking App Sections:")
    all_found = True
    
    for section_text, description in sections:
        if section_text in content:
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
            all_found = False
    
    # Check the flow order
    print(f"\n📍 Flow Order Check:")
    
    # Find line numbers for key sections
    lines = content.split('\n')
    key_lines = {}
    
    for i, line in enumerate(lines):
        if "Choose Masking Mode" in line:
            key_lines["mode_selection"] = i + 1
        elif "Generate AI Suggestions" in line:
            key_lines["generation_button"] = i + 1
        elif "Customize Your Masking" in line:
            key_lines["customization"] = i + 1
        elif "Apply Customized Masking" in line:
            key_lines["apply_button"] = i + 1
    
    print(f"Mode Selection: Line {key_lines.get('mode_selection', 'NOT FOUND')}")
    print(f"Generation Button: Line {key_lines.get('generation_button', 'NOT FOUND')}")
    print(f"Customization: Line {key_lines.get('customization', 'NOT FOUND')}")
    print(f"Apply Button: Line {key_lines.get('apply_button', 'NOT FOUND')}")
    
    # Check logical flow
    if all(key in key_lines for key in ['mode_selection', 'generation_button', 'customization', 'apply_button']):
        flow_correct = (
            key_lines['mode_selection'] < key_lines['generation_button'] < 
            key_lines['customization'] < key_lines['apply_button']
        )
        print(f"\n📊 Flow Order: {'✅ Correct' if flow_correct else '❌ Incorrect'}")
    else:
        print(f"\n📊 Flow Order: ❌ Missing sections")
        all_found = False
    
    return all_found

def show_user_instructions():
    """Show instructions for using the app"""
    print(f"\n" + "=" * 50)
    print("📖 USER INSTRUCTIONS")
    print("=" * 50)
    
    print(f"\n🚀 How to See the Generation Buttons:")
    print(f"1. Run: streamlit run app.py")
    print(f"2. Wait for the app to load")
    print(f"3. Choose masking mode (Single Table or Multi-Table)")
    print(f"4. Select your table(s) from the dropdown")
    print(f"5. 📍 GENERATION BUTTONS APPEAR HERE:")
    print(f"   🤖 Generate AI Suggestions (GPT + Healthcare Rules)")
    print(f"   ⚡ Apply Healthcare Rules (Fast Mode)")
    print(f"   🧹 Clear Cache")
    
    print(f"\n💡 If you don't see the buttons:")
    print(f"• Make sure you've selected a table first")
    print(f"• Scroll down - they appear after table selection")
    print(f"• Check that the database connection is working")
    print(f"• Try refreshing the page")
    
    print(f"\n🎯 Complete Flow:")
    print(f"1. Select masking mode")
    print(f"2. Select table(s)")
    print(f"3. 🤖 Generate suggestions ← GENERATION PART")
    print(f"4. ⚙️ Customize masking")
    print(f"5. 🎭 Apply masking")
    print(f"6. 📥 Download results")

def main():
    """Run the app structure test"""
    print("🔍 App Structure Verification")
    print("=" * 30)
    
    structure_ok = test_app_structure()
    
    if structure_ok:
        print(f"\n🎉 SUCCESS!")
        print(f"✅ All generation features are present in the app")
        print(f"✅ Flow order is correct")
        print(f"✅ App structure is complete")
        
        show_user_instructions()
        
        print(f"\n🎯 THE GENERATION PART IS DEFINITELY THERE!")
        print(f"The buttons appear after you select a table.")
        
    else:
        print(f"\n⚠️ Some issues found in app structure")
        print(f"Check the missing sections above")

if __name__ == "__main__":
    main()
