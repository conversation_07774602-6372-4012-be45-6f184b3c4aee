#!/usr/bin/env python3
"""
Healthcare Data Masking Demo
Demonstrates how the comprehensive masking system works with typical hospital management system data
"""

import pandas as pd
from datetime import datetime, date
from masking_engine import mask_column, apply_masking

def create_sample_hospital_data():
    """Create sample hospital management system data"""
    return pd.DataFrame({
        # Patient Demographics
        'patient_id': ['P001', 'P002', 'P003', 'P004', 'P005'],
        'first_name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'last_name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'date_of_birth': [
            datetime(1985, 3, 15),
            datetime(1992, 7, 22),
            datetime(1978, 11, 8),
            datetime(1965, 5, 30),
            datetime(2000, 9, 12)
        ],
        'ssn': ['***********', '***********', '***********', '***********', '***********'],
        'phone': ['555-0123', '555-0456', '555-0789', '555-0321', '555-0654'],
        'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'address': ['123 Main St, City, ST 12345', '456 Oak Ave, Town, ST 67890', '789 Pine Rd, Village, ST 54321', '321 Elm St, Borough, ST 98765', '654 Maple Dr, Hamlet, ST 13579'],
        
        # Medical Information
        'medical_record_number': ['MRN001', 'MRN002', 'MRN003', 'MRN004', 'MRN005'],
        'diagnosis_code': ['E11.9', 'I10', 'J44.1', 'M79.3', 'F32.9'],
        'admission_date': [
            datetime(2024, 1, 15, 9, 30),
            datetime(2024, 2, 20, 14, 15),
            datetime(2024, 3, 10, 11, 45),
            datetime(2024, 4, 5, 16, 20),
            datetime(2024, 5, 12, 8, 10)
        ],
        'discharge_date': [
            datetime(2024, 1, 18, 10, 0),
            datetime(2024, 2, 25, 12, 30),
            datetime(2024, 3, 15, 9, 15),
            datetime(2024, 4, 8, 14, 45),
            datetime(2024, 5, 15, 16, 30)
        ],
        'room_number': ['101A', '205B', '312C', '150A', '278B'],
        'attending_physician': ['Dr. Anderson', 'Dr. Martinez', 'Dr. Thompson', 'Dr. Wilson', 'Dr. Garcia'],
        
        # Financial Information
        'insurance_id': ['INS123456', 'INS789012', 'INS345678', 'INS901234', 'INS567890'],
        'total_charges': [15420.50, 8750.25, 22100.75, 5680.00, 12350.80],
        'copay_amount': [50.00, 25.00, 100.00, 30.00, 75.00],
        
        # Clinical Data
        'blood_pressure_systolic': [120, 140, 110, 160, 125],
        'blood_pressure_diastolic': [80, 90, 70, 95, 82],
        'heart_rate': [72, 85, 68, 92, 78],
        'temperature': [98.6, 99.2, 97.8, 100.1, 98.4],
        'weight_lbs': [165, 140, 180, 200, 155],
        
        # Sensitive Notes
        'clinical_notes': [
            'Patient presents with diabetes symptoms',
            'Hypertension management ongoing',
            'COPD exacerbation, stable condition',
            'Chronic pain management',
            'Depression screening completed'
        ]
    })

def demonstrate_healthcare_masking():
    """Demonstrate various masking strategies for healthcare data"""
    
    print("🏥 HEALTHCARE DATA MASKING DEMONSTRATION")
    print("=" * 60)
    
    # Create sample data
    df = create_sample_hospital_data()
    
    print("\n📋 ORIGINAL HOSPITAL DATA (Sample):")
    print(df.head(2).to_string())
    
    # Define masking strategies for different compliance levels
    masking_strategies = {
        'HIPAA_COMPLIANT': {
            # Patient Demographics - High Security
            'patient_id': 'tokenize',
            'first_name': 'faker.first_name()',
            'last_name': 'faker.last_name()',
            'date_of_birth': 'age_group',  # Convert to age groups instead of exact dates
            'ssn': 'hash_sha256',  # Irreversible hash
            'phone': 'faker.phone_number()',
            'email': 'faker.email()',
            'address': 'faker.address()',
            
            # Medical Information - Preserve some utility
            'medical_record_number': 'tokenize',
            'diagnosis_code': 'no masking',  # Keep for medical analysis
            'admission_date': 'shift_days_random',  # Shift dates but preserve temporal relationships
            'discharge_date': 'shift_days_random',
            'room_number': 'scramble',
            'attending_physician': 'faker.name()',
            
            # Financial - Preserve statistical properties
            'insurance_id': 'tokenize',
            'total_charges': 'add_noise_percentage',  # Add noise but preserve trends
            'copay_amount': 'round_to_nearest_10',
            
            # Clinical Data - Preserve for research
            'blood_pressure_systolic': 'add_noise_fixed',
            'blood_pressure_diastolic': 'add_noise_fixed',
            'heart_rate': 'add_noise_fixed',
            'temperature': 'add_noise_percentage',
            'weight_lbs': 'add_noise_percentage',
            
            # Sensitive Notes
            'clinical_notes': 'redact'
        },
        
        'RESEARCH_FRIENDLY': {
            # Less aggressive masking for research purposes
            'patient_id': 'deterministic_hash',  # Consistent across datasets
            'first_name': 'faker.first_name()',
            'last_name': 'faker.last_name()',
            'date_of_birth': 'year_only',  # Keep birth year for age analysis
            'ssn': 'hash_sha256',
            'phone': 'mask_partial',
            'email': 'mask_partial',
            'address': 'faker.city()',  # Keep geographic region
            
            'medical_record_number': 'deterministic_hash',
            'diagnosis_code': 'no masking',
            'admission_date': 'month_year_only',  # Preserve seasonal patterns
            'discharge_date': 'month_year_only',
            'room_number': 'scramble',
            'attending_physician': 'faker.name()',
            
            'insurance_id': 'deterministic_hash',
            'total_charges': 'categorical_binning',  # Low/Medium/High categories
            'copay_amount': 'categorical_binning',
            
            # Preserve clinical data with minimal noise
            'blood_pressure_systolic': 'round_to_nearest_10',
            'blood_pressure_diastolic': 'round_to_nearest_10',
            'heart_rate': 'round_to_nearest_10',
            'temperature': 'no masking',  # Important for medical research
            'weight_lbs': 'round_to_nearest_10',
            
            'clinical_notes': 'scramble'
        },
        
        'TESTING_ENVIRONMENT': {
            # Realistic fake data for testing
            'patient_id': 'faker.random_number()',
            'first_name': 'faker.first_name()',
            'last_name': 'faker.last_name()',
            'date_of_birth': 'faker.date_of_birth()',
            'ssn': 'faker.ssn()',
            'phone': 'faker.phone_number()',
            'email': 'faker.email()',
            'address': 'faker.address()',
            
            'medical_record_number': 'faker.random_number()',
            'diagnosis_code': 'no masking',  # Keep real codes for testing
            'admission_date': 'faker.date_time_this_year()',
            'discharge_date': 'faker.date_time_this_year()',
            'room_number': 'faker.random_number()',
            'attending_physician': 'faker.name()',
            
            'insurance_id': 'faker.random_number()',
            'total_charges': 'faker.pyfloat()',
            'copay_amount': 'faker.pyfloat()',
            
            'blood_pressure_systolic': 'faker.random_int()',
            'blood_pressure_diastolic': 'faker.random_int()',
            'heart_rate': 'faker.random_int()',
            'temperature': 'faker.pyfloat()',
            'weight_lbs': 'faker.random_int()',
            
            'clinical_notes': 'faker.catch_phrase()'
        }
    }
    
    # Demonstrate each masking strategy
    for strategy_name, strategy in masking_strategies.items():
        print(f"\n🔒 {strategy_name} MASKING STRATEGY:")
        print("-" * 50)
        
        # Create masking plan
        masking_plan = []
        for column, method in strategy.items():
            masking_plan.append({
                'column': column,
                'suggested_masking': method
            })
        
        # Apply masking
        masked_df = apply_masking(df.copy(), masking_plan)
        
        # Show key columns
        key_columns = ['patient_id', 'first_name', 'last_name', 'date_of_birth', 'ssn', 'total_charges', 'clinical_notes']
        available_columns = [col for col in key_columns if col in masked_df.columns]
        
        print(masked_df[available_columns].head(2).to_string())
        
        print(f"\n✅ {strategy_name} masking completed successfully!")

def show_healthcare_compliance_benefits():
    """Show how this system helps with healthcare compliance"""
    
    print("\n🛡️ HEALTHCARE COMPLIANCE BENEFITS:")
    print("=" * 50)
    
    compliance_features = {
        "HIPAA Compliance": [
            "✅ De-identification of 18 HIPAA identifiers",
            "✅ Safe Harbor method implementation",
            "✅ Irreversible hashing for sensitive data",
            "✅ Date shifting to preserve temporal relationships",
            "✅ Statistical disclosure control"
        ],
        "GDPR Compliance": [
            "✅ Right to erasure (data can be nullified)",
            "✅ Data minimization (only necessary data preserved)",
            "✅ Pseudonymization techniques",
            "✅ Privacy by design implementation"
        ],
        "Research Ethics": [
            "✅ IRB-compliant data de-identification",
            "✅ Preserve statistical utility for research",
            "✅ Consistent pseudonyms across datasets",
            "✅ Differential privacy options available"
        ],
        "Operational Security": [
            "✅ Multiple security levels (High/Medium/Low)",
            "✅ Reversible encryption for authorized access",
            "✅ Audit trail of masking operations",
            "✅ Batch processing for large datasets"
        ]
    }
    
    for category, features in compliance_features.items():
        print(f"\n📋 {category}:")
        for feature in features:
            print(f"   {feature}")

if __name__ == "__main__":
    demonstrate_healthcare_masking()
    show_healthcare_compliance_benefits()
    
    print("\n" + "=" * 60)
    print("🎯 RECOMMENDATION FOR HOSPITAL MANAGEMENT SYSTEM:")
    print("=" * 60)
    print("""
    1. Use HIPAA_COMPLIANT strategy for production data sharing
    2. Use RESEARCH_FRIENDLY for approved research projects  
    3. Use TESTING_ENVIRONMENT for development/testing
    4. Always hash SSNs and medical record numbers
    5. Use age_group instead of exact birth dates
    6. Shift dates consistently to preserve treatment timelines
    7. Add noise to financial data to prevent re-identification
    8. Redact or scramble free-text clinical notes
    """)
