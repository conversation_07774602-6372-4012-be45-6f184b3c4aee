import os
import json
import psycopg2
import pandas as pd
from dotenv import load_dotenv
from datetime import timedelta, datetime
import random
import hashlib
import uuid
import re
import numpy as np
from faker import Faker
from openai import OpenAI
from cryptography.fernet import Fernet
import base64

load_dotenv()
fake = Faker()

# OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# --- Connect to PostgreSQL ---
def get_connection():
    return psycopg2.connect(
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_NAME"),
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD")
    )

# --- Get Schema + Sample Data ---
def get_schema_and_samples(table_name, limit=5):
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute(f"""
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = '{table_name}';
    """)
    schema = cursor.fetchall()

    df = pd.read_sql(f"SELECT * FROM {table_name} LIMIT {limit};", conn)

    cursor.close()
    conn.close()
    return schema, df

# --- Cache File ---
CACHE_FILE = "masking_gpt_cache.json"

def load_cache():
    return json.load(open(CACHE_FILE)) if os.path.exists(CACHE_FILE) else {}

def save_cache(cache):
    with open(CACHE_FILE, "w") as f:
        json.dump(cache, f, indent=2)

# --- GPT Call with Safe JSON Decoding ---
def ask_gpt_for_masking(col_name, sql_type, values):
    prompt = f"""
You are a data security expert.

Given the following:
- Column Name: {col_name}
- SQL Type: {sql_type}
- Sample Values: {values}

Reply ONLY with this valid JSON format (no markdown or explanation):

{{
  "logical_type": "string",
  "suggested_masking": "faker.name()"  // or hash, shift date, redact, etc.
}}
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2
        )
        raw = response.choices[0].message.content.strip()
        print(f"\n🧠 GPT Response for column `{col_name}`:\n{raw}\n")
        return json.loads(raw)

    except Exception as e:
        print(f"⚠️ Error parsing GPT response for column `{col_name}`: {e}")
        return {
            "logical_type": "unknown",
            "suggested_masking": "no masking"
        }

# --- Masking Plan ---
def get_masking_plan(schema, df):
    cache = load_cache()
    plan = []

    for col, sql_type in schema:
        key = f"{col}|{sql_type}"
        if key in cache:
            plan.append({**cache[key], "column": col})
            continue

        values = df[col].dropna().astype(str).tolist()[:5]
        if not values:
            continue

        result = ask_gpt_for_masking(col, sql_type, values)
        cache[key] = result
        plan.append({**result, "column": col})

    save_cache(cache)
    return plan

# --- Text/String Masking Helper Functions ---
def scramble_text(val):
    return ''.join(random.sample(str(val), len(str(val)))) if isinstance(val, str) and len(str(val)) > 1 else val

def shuffle_words(val):
    """Shuffle words in a sentence"""
    if not isinstance(val, str):
        return val
    words = str(val).split()
    if len(words) > 1:
        random.shuffle(words)
        return ' '.join(words)
    return val

def mask_partial(val):
    """Mask middle characters (Jo***hn)"""
    if not isinstance(val, str) or len(str(val)) <= 2:
        return val
    s = str(val)
    if len(s) <= 4:
        return s[0] + '*' * (len(s) - 2) + s[-1]
    else:
        return s[:2] + '*' * (len(s) - 4) + s[-2:]

def mask_first_half(val):
    """Mask first half of string"""
    if not isinstance(val, str):
        return val
    s = str(val)
    mid = len(s) // 2
    return '*' * mid + s[mid:]

def mask_last_half(val):
    """Mask last half of string"""
    if not isinstance(val, str):
        return val
    s = str(val)
    mid = len(s) // 2
    return s[:mid] + '*' * (len(s) - mid)

def deterministic_hash(val, salt="default_salt"):
    """Generate deterministic hash - same input always produces same output"""
    return hashlib.sha256((str(val) + salt).encode()).hexdigest()[:12]

def random_string_same_length(val):
    """Generate random string of same length"""
    if not isinstance(val, str):
        return val
    import string
    length = len(str(val))
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def random_string_fixed_length(val, length=8):
    """Generate random string of fixed length"""
    import string
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def character_substitution(val):
    """Replace specific characters with similar ones"""
    if not isinstance(val, str):
        return val
    substitutions = {
        'a': '@', 'e': '3', 'i': '1', 'o': '0', 's': '$',
        'A': '@', 'E': '3', 'I': '1', 'O': '0', 'S': '$'
    }
    result = str(val)
    for old, new in substitutions.items():
        result = result.replace(old, new)
    return result

def encrypt_aes(val, key=None):
    """AES encryption (requires key for decryption)"""
    if key is None:
        key = Fernet.generate_key()
    f = Fernet(key)
    encrypted = f.encrypt(str(val).encode())
    return base64.urlsafe_b64encode(encrypted).decode()

# --- Numeric Masking Helper Functions ---
def add_noise_percentage(val, percentage=10):
    """Add random noise as percentage of original value"""
    if not isinstance(val, (int, float)):
        return val
    noise_factor = percentage / 100.0
    noise = random.uniform(-noise_factor, noise_factor) * val
    return val + noise

def add_noise_fixed(val, amount=10):
    """Add fixed amount of random noise"""
    if not isinstance(val, (int, float)):
        return val
    noise = random.uniform(-amount, amount)
    return val + noise

def multiply_by_factor(val, min_factor=0.8, max_factor=1.2):
    """Multiply by random factor"""
    if not isinstance(val, (int, float)):
        return val
    factor = random.uniform(min_factor, max_factor)
    return val * factor

def round_to_nearest(val, nearest=10):
    """Round to nearest specified value"""
    if not isinstance(val, (int, float)):
        return val
    return round(val / nearest) * nearest

def binning(val, bins=None):
    """Convert numeric value to categorical bins"""
    if not isinstance(val, (int, float)):
        return val
    if bins is None:
        bins = [0, 25, 50, 75, 100]

    for i in range(len(bins) - 1):
        if bins[i] <= val < bins[i + 1]:
            return f"{bins[i]}-{bins[i + 1] - 1}"
    return f"{bins[-1]}+"

def rank_transformation(val, reference_values):
    """Replace with rank/percentile"""
    if not isinstance(val, (int, float)) or not reference_values:
        return val
    sorted_vals = sorted([v for v in reference_values if isinstance(v, (int, float))])
    if not sorted_vals:
        return val

    rank = sum(1 for v in sorted_vals if v <= val)
    percentile = (rank / len(sorted_vals)) * 100
    return f"P{int(percentile)}"

def random_in_range(val, reference_values):
    """Generate random number within min-max range of reference values"""
    if not isinstance(val, (int, float)) or not reference_values:
        return val

    numeric_vals = [v for v in reference_values if isinstance(v, (int, float))]
    if not numeric_vals:
        return val

    min_val, max_val = min(numeric_vals), max(numeric_vals)
    if isinstance(val, int):
        return random.randint(int(min_val), int(max_val))
    else:
        return random.uniform(min_val, max_val)

def categorical_binning(val):
    """Convert numeric to Low/Medium/High categories"""
    if not isinstance(val, (int, float)):
        return val

    # This is a simple implementation - in practice, you'd want to determine
    # thresholds based on the data distribution
    if val < 33:
        return "Low"
    elif val < 67:
        return "Medium"
    else:
        return "High"

# --- Date/DateTime Masking Helper Functions ---
def shift_days_random(val, min_days=-30, max_days=30):
    """Shift date by random number of days"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_days, max_days)
    return val + timedelta(days=shift)

def shift_months_random(val, min_months=-6, max_months=6):
    """Shift date by random number of months"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_months, max_months)
    # Approximate month shift (30 days per month)
    return val + timedelta(days=shift * 30)

def shift_years_random(val, min_years=-2, max_years=2):
    """Shift date by random number of years"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_years, max_years)
    return val + timedelta(days=shift * 365)

def shift_days_fixed(val, days=30):
    """Shift date by fixed number of days"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val + timedelta(days=days)

def year_only(val):
    """Keep only year (set to January 1st)"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

def month_year_only(val):
    """Keep only month and year (set to 1st of month)"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

def quarter_year_only(val):
    """Keep only quarter and year"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    quarter = (val.month - 1) // 3 + 1
    return f"Q{quarter} {val.year}"

def day_of_week_only(val):
    """Keep only day of week"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.strftime("%A")

def season_only(val):
    """Keep only season"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    month = val.month
    if month in [12, 1, 2]:
        return "Winter"
    elif month in [3, 4, 5]:
        return "Spring"
    elif month in [6, 7, 8]:
        return "Summer"
    else:
        return "Fall"

def age_group(val):
    """Convert birth date to age group"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    today = datetime.now()
    age = today.year - val.year - ((today.month, today.day) < (val.month, val.day))

    if age < 18:
        return "Under 18"
    elif age < 25:
        return "18-24"
    elif age < 35:
        return "25-34"
    elif age < 45:
        return "35-44"
    elif age < 55:
        return "45-54"
    elif age < 65:
        return "55-64"
    else:
        return "65+"

def remove_time_component(val):
    """Keep only date part"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.date()

def remove_date_component(val):
    """Keep only time part"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.time()

def round_to_hour(val):
    """Round to nearest hour"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(minute=0, second=0, microsecond=0)

def round_to_day(val):
    """Round to nearest day"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(hour=0, minute=0, second=0, microsecond=0)

def randomize_time_component(val):
    """Keep date, randomize time"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    random_hour = random.randint(0, 23)
    random_minute = random.randint(0, 59)
    random_second = random.randint(0, 59)

    return val.replace(hour=random_hour, minute=random_minute, second=random_second, microsecond=0)

# --- Boolean Masking Helper Functions ---
def random_boolean(val):
    """Generate random boolean"""
    return random.choice([True, False])

def flip_percentage(val, percentage=10):
    """Flip boolean value X% of the time"""
    if not isinstance(val, bool):
        return val

    if random.randint(1, 100) <= percentage:
        return not val
    return val

def maintain_boolean_distribution(val, reference_values):
    """Maintain same true/false ratio as reference data"""
    if not reference_values:
        return random.choice([True, False])

    bool_vals = [v for v in reference_values if isinstance(v, bool)]
    if not bool_vals:
        return random.choice([True, False])

    true_ratio = sum(bool_vals) / len(bool_vals)
    return random.random() < true_ratio

# --- Special/Composite Data Type Masking Functions ---
def mask_json_fields(val, fields_to_mask=None):
    """Mask specific fields in JSON data"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, str):
            data = json.loads(val)
        else:
            data = val.copy()

        if fields_to_mask is None:
            fields_to_mask = ['name', 'email', 'phone', 'address']

        def mask_recursive(obj):
            if isinstance(obj, dict):
                for key in obj:
                    if key.lower() in [f.lower() for f in fields_to_mask]:
                        obj[key] = "[MASKED]"
                    elif isinstance(obj[key], (dict, list)):
                        mask_recursive(obj[key])
            elif isinstance(obj, list):
                for item in obj:
                    if isinstance(item, (dict, list)):
                        mask_recursive(item)

        mask_recursive(data)
        return json.dumps(data) if isinstance(val, str) else data

    except (json.JSONDecodeError, TypeError):
        return val

def scramble_json_values(val):
    """Scramble all string values in JSON"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, str):
            data = json.loads(val)
        else:
            data = val.copy()

        def scramble_recursive(obj):
            if isinstance(obj, dict):
                for key in obj:
                    if isinstance(obj[key], str):
                        obj[key] = scramble_text(obj[key])
                    elif isinstance(obj[key], (dict, list)):
                        scramble_recursive(obj[key])
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, str):
                        obj[i] = scramble_text(item)
                    elif isinstance(item, (dict, list)):
                        scramble_recursive(item)

        scramble_recursive(data)
        return json.dumps(data) if isinstance(val, str) else data

    except (json.JSONDecodeError, TypeError):
        return val

def hash_json_object(val):
    """Hash entire JSON object"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, dict):
            json_str = json.dumps(val, sort_keys=True)
        else:
            json_str = val

        return hashlib.sha256(json_str.encode()).hexdigest()

    except (TypeError, json.JSONDecodeError):
        return val

def shuffle_array(val):
    """Shuffle array elements"""
    if not isinstance(val, (list, str)):
        return val

    try:
        if isinstance(val, str):
            # Try to parse as JSON array
            data = json.loads(val)
            if isinstance(data, list):
                random.shuffle(data)
                return json.dumps(data)
            return val
        else:
            # Direct list
            shuffled = val.copy()
            random.shuffle(shuffled)
            return shuffled

    except (json.JSONDecodeError, TypeError):
        return val

def random_array_same_size(val):
    """Generate random array of same size"""
    if not isinstance(val, (list, str)):
        return val

    try:
        if isinstance(val, str):
            # Try to parse as JSON array
            data = json.loads(val)
            if isinstance(data, list):
                # Generate random array of same length
                random_data = [random.randint(1, 100) for _ in range(len(data))]
                return json.dumps(random_data)
            return val
        else:
            # Direct list
            return [random.randint(1, 100) for _ in range(len(val))]

    except (json.JSONDecodeError, TypeError):
        return val

# --- Advanced Utility Functions ---
def format_preserving_encrypt_digits(val, key=None):
    """Simple format-preserving encryption for numeric strings"""
    if not isinstance(val, str) or not val.isdigit():
        return val

    # Simple digit substitution (not cryptographically secure)
    digit_map = {
        '0': '7', '1': '4', '2': '9', '3': '1', '4': '8',
        '5': '2', '6': '0', '7': '5', '8': '3', '9': '6'
    }

    return ''.join(digit_map.get(d, d) for d in val)

def add_laplace_noise(val, sensitivity=1.0, epsilon=1.0):
    """Add Laplace noise for differential privacy"""
    if not isinstance(val, (int, float)):
        return val

    # Laplace noise: scale = sensitivity / epsilon
    scale = sensitivity / epsilon
    noise = np.random.laplace(0, scale)
    return val + noise

def add_gaussian_noise(val, sensitivity=1.0, epsilon=1.0, delta=1e-5):
    """Add Gaussian noise for differential privacy"""
    if not isinstance(val, (int, float)):
        return val

    # Gaussian noise for differential privacy
    sigma = (sensitivity * np.sqrt(2 * np.log(1.25 / delta))) / epsilon
    noise = np.random.normal(0, sigma)
    return val + noise

def generate_synthetic_similar(val, reference_values=None):
    """Generate synthetic data with similar properties"""
    if isinstance(val, str):
        # For strings, generate similar length and character types
        if not val:
            return ""

        import string
        char_types = []
        if any(c.isalpha() for c in val):
            char_types.extend(string.ascii_letters)
        if any(c.isdigit() for c in val):
            char_types.extend(string.digits)
        if any(c in string.punctuation for c in val):
            char_types.extend('.-_@')

        if not char_types:
            char_types = string.ascii_letters

        return ''.join(random.choices(char_types, k=len(val)))

    elif isinstance(val, (int, float)):
        # For numbers, generate within similar range
        if reference_values:
            numeric_refs = [v for v in reference_values if isinstance(v, (int, float))]
            if numeric_refs:
                min_val, max_val = min(numeric_refs), max(numeric_refs)
                if isinstance(val, int):
                    return random.randint(int(min_val), int(max_val))
                else:
                    return random.uniform(min_val, max_val)

        # Fallback: generate within ±50% of original value
        if isinstance(val, int):
            return random.randint(int(val * 0.5), int(val * 1.5))
        else:
            return random.uniform(val * 0.5, val * 1.5)

    return val

def preserve_data_relationships(df, columns, method="correlation"):
    """Preserve relationships between columns during masking"""
    if method == "correlation" and len(columns) >= 2:
        # Calculate correlation matrix
        numeric_cols = [col for col in columns if df[col].dtype in ['int64', 'float64']]
        if len(numeric_cols) >= 2:
            corr_matrix = df[numeric_cols].corr()
            return corr_matrix

    return None

def maintain_column_statistics(original_values, masked_values):
    """Check if statistical properties are maintained"""
    if not original_values or not masked_values:
        return {}

    orig_numeric = [v for v in original_values if isinstance(v, (int, float))]
    mask_numeric = [v for v in masked_values if isinstance(v, (int, float))]

    if not orig_numeric or not mask_numeric:
        return {}

    stats = {
        'original_mean': np.mean(orig_numeric),
        'masked_mean': np.mean(mask_numeric),
        'original_std': np.std(orig_numeric),
        'masked_std': np.std(mask_numeric),
        'mean_preserved': abs(np.mean(orig_numeric) - np.mean(mask_numeric)) < 0.1 * np.mean(orig_numeric),
        'std_preserved': abs(np.std(orig_numeric) - np.std(mask_numeric)) < 0.1 * np.std(orig_numeric)
    }

    return stats

def mask_column(val, method, reference_values=None):
    if pd.isnull(val):
        return val

    try:
        # === FAKER METHODS ===
        if method.startswith("faker."):
            method_name = method.replace("faker.", "").replace("()", "")
            faker_fn = getattr(fake, method_name, None)
            if callable(faker_fn):
                return faker_fn()

        # === TEXT/STRING MASKING ===
        elif method == "hash_sha256":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "hash_md5":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "hash_sha1":
            return hashlib.sha1(str(val).encode()).hexdigest()
        elif method == "scramble":
            return scramble_text(val)
        elif method == "shuffle_words":
            return shuffle_words(val)
        elif method == "mask_out":
            return '*' * len(str(val))
        elif method == "mask_partial":
            return mask_partial(val)
        elif method == "mask_first_half":
            return mask_first_half(val)
        elif method == "mask_last_half":
            return mask_last_half(val)
        elif method == "redact":
            return "[REDACTED]"
        elif method == "nullify":
            return None
        elif method == "tokenize":
            return str(uuid.uuid4())[:12]
        elif method == "encrypt_aes":
            return encrypt_aes(val)
        elif method == "deterministic_hash":
            return deterministic_hash(val)
        elif method == "random_string_same_length":
            return random_string_same_length(val)
        elif method == "random_string_fixed_length":
            return random_string_fixed_length(val)
        elif method == "substitute_from_list" and reference_values:
            return random.choice(reference_values)
        elif method == "character_substitution":
            return character_substitution(val)

        # === NUMERIC MASKING ===
        elif method == "add_noise_percentage":
            return add_noise_percentage(val)
        elif method == "add_noise_fixed":
            return add_noise_fixed(val)
        elif method == "multiply_by_factor":
            return multiply_by_factor(val)
        elif method == "round_to_nearest_10":
            return round_to_nearest(val, 10)
        elif method == "round_to_nearest_100":
            return round_to_nearest(val, 100)
        elif method == "round_to_nearest_1000":
            return round_to_nearest(val, 1000)
        elif method == "binning":
            return binning(val)
        elif method == "rank_transformation" and reference_values:
            return rank_transformation(val, reference_values)
        elif method == "random_in_range" and reference_values:
            return random_in_range(val, reference_values)
        elif method == "random_in_percentile_range" and reference_values:
            return random_in_range(val, reference_values)  # Same implementation for now
        elif method == "categorical_binning":
            return categorical_binning(val)
        elif method == "maintain_distribution" and reference_values:
            return random_in_range(val, reference_values)  # Simplified implementation
        elif method == "variance":
            if isinstance(val, (int, float)):
                noise = random.uniform(-0.1, 0.1) * val
                return val + noise

        # === DATE/DATETIME MASKING ===
        elif method == "shift_days_random":
            return shift_days_random(val)
        elif method == "shift_months_random":
            return shift_months_random(val)
        elif method == "shift_years_random":
            return shift_years_random(val)
        elif method == "shift_days_fixed":
            return shift_days_fixed(val)
        elif method == "shift_consistent":
            return shift_days_fixed(val, 30)  # Consistent 30-day shift
        elif method == "year_only":
            return year_only(val)
        elif method == "month_year_only":
            return month_year_only(val)
        elif method == "quarter_year_only":
            return quarter_year_only(val)
        elif method == "day_of_week_only":
            return day_of_week_only(val)
        elif method == "season_only":
            return season_only(val)
        elif method == "age_group":
            return age_group(val)
        elif method == "remove_time_component":
            return remove_time_component(val)
        elif method == "remove_date_component":
            return remove_date_component(val)
        elif method == "round_to_hour":
            return round_to_hour(val)
        elif method == "round_to_day":
            return round_to_day(val)
        elif method == "randomize_time_component":
            return randomize_time_component(val)

        # === BOOLEAN MASKING ===
        elif method == "random_boolean":
            return random_boolean(val)
        elif method == "flip_percentage":
            return flip_percentage(val)
        elif method == "maintain_boolean_distribution" and reference_values:
            return maintain_boolean_distribution(val, reference_values)

        # === SPECIAL/COMPOSITE DATA TYPES ===
        elif method == "mask_json_fields":
            return mask_json_fields(val)
        elif method == "scramble_json_values":
            return scramble_json_values(val)
        elif method == "hash_json_object":
            return hash_json_object(val)
        elif method == "shuffle_array":
            return shuffle_array(val)
        elif method == "random_array_same_size":
            return random_array_same_size(val)

        # === ADVANCED TECHNIQUES ===
        elif method == "format_preserving_encrypt":
            return format_preserving_encrypt_digits(val)
        elif method == "laplace_noise":
            return add_laplace_noise(val)
        elif method == "gaussian_noise":
            return add_gaussian_noise(val)
        elif method == "synthetic_similar" and reference_values:
            return generate_synthetic_similar(val, reference_values)

        # === LEGACY/BACKWARD COMPATIBILITY ===
        elif method == "hash":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "encrypt":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "substitute" and reference_values:
            return random.choice(reference_values)
        elif "shift date" in method.lower():
            shift = random.randint(-5, 5)
            return val + timedelta(days=shift)

        # === NO MASKING ===
        elif method == "no masking":
            return val

    except Exception as e:
        print(f"⚠️ Error masking value `{val}` with method `{method}`: {e}")
        return val

    return val  # fallback

def apply_masking(df, masking_plan):
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']

        # Special handling for shuffle - operates on entire column
        if method == "shuffle":
            df[col] = df[col].sample(frac=1).reset_index(drop=True)
        else:
            # Determine if method needs reference values
            methods_needing_reference = [
                "substitute", "substitute_from_list", "rank_transformation",
                "random_in_range", "random_in_percentile_range",
                "maintain_distribution", "maintain_boolean_distribution"
            ]

            reference_values = None
            if any(ref_method in method for ref_method in methods_needing_reference):
                reference_values = df[col].dropna().tolist()

            # Apply masking to each value in the column
            df[col] = df[col].apply(lambda v: mask_column(v, method, reference_values))

    return df
