import os
import json
import psycopg2
import pandas as pd
from dotenv import load_dotenv
from datetime import timedelta, datetime
import random
import hashlib
import uuid
import re
import numpy as np
from faker import Faker
from openai import OpenAI
from cryptography.fernet import Fernet
import base64

load_dotenv()
fake = Faker()

# OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# --- Connect to PostgreSQL ---
def get_connection():
    return psycopg2.connect(
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_NAME"),
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD")
    )

# --- Get Schema + Sample Data ---
def get_schema_and_samples(table_name, limit=5):
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute(f"""
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = '{table_name}';
    """)
    schema = cursor.fetchall()

    df = pd.read_sql(f"SELECT * FROM {table_name} LIMIT {limit};", conn)

    cursor.close()
    conn.close()
    return schema, df

# --- Cache File ---
CACHE_FILE = "masking_gpt_cache.json"

def load_cache():
    return json.load(open(CACHE_FILE)) if os.path.exists(CACHE_FILE) else {}

def save_cache(cache):
    with open(CACHE_FILE, "w") as f:
        json.dump(cache, f, indent=2)

# --- GPT Call with Safe JSON Decoding ---
def ask_gpt_for_masking(col_name, sql_type, values):
    prompt = f"""Healthcare data masking expert. Analyze: {col_name} ({sql_type}) with samples: {values[:3]}

CRITICAL ID DETECTION:
- If samples look like IDs (mix of letters/numbers, consistent format like "CLMEA1A949D", "PAT123456") → "format_preserving_id"
- Examples: claim_id, patient_id, provider_id, member_id, account_id → "format_preserving_id"

OTHER RULES:
- Patient/doctor names (like "John Doe") → "faker.name()"
- Medical record numbers → "deterministic_hash"
- Birth dates → "hipaa_age_group"
- Clinical notes → "mask_clinical_notes"
- Lab values → "mask_lab_results"
- Insurance → "hash_sha256"
- Dates → "shift_days_random"
- Phone → "faker.phone_number()"
- Email → "faker.email()"
- Status/Codes → "no masking"

JSON only:
{{"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}}"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2
        )
        raw = response.choices[0].message.content.strip()
        print(f"\n🧠 GPT Response for column `{col_name}`:\n{raw}\n")
        return json.loads(raw)

    except Exception as e:
        print(f"⚠️ Error parsing GPT response for column `{col_name}`: {e}")
        return {
            "logical_type": "unknown",
            "suggested_masking": "no masking"
        }

# --- Healthcare-Specific Rules ---
def get_healthcare_fallback(col_name, sql_type, values=None):
    """Healthcare-specific fallback rules for common medical data"""
    col_lower = col_name.lower()

    # PRIORITY 1: Check for ID patterns first (most important for your use case)
    id_keywords = ['id', 'number', 'code', 'claim', 'member', 'account', 'provider', 'patient', 'mrn']
    if any(keyword in col_lower for keyword in id_keywords):
        # ALWAYS check if values look like structured IDs first
        if values and analyze_id_pattern(values):
            return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}
        # Only use hash for patient_id if it doesn't look like a structured ID
        elif any(term in col_lower for term in ['patient_id', 'mrn', 'medical_record']) and values and not analyze_id_pattern(values):
            return {"logical_type": "identifier", "suggested_masking": "deterministic_hash"}

    # PRIORITY 2: Check if values look like IDs even if column name doesn't clearly indicate it
    if values and analyze_id_pattern(values):
        return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}

    # PRIORITY 3: Name detection (only for clearly name-like data)
    if any(term in col_lower for term in ['name', 'first_name', 'last_name']) and values:
        # Double-check this isn't actually an ID that happens to have 'name' in column name
        if not analyze_id_pattern(values):
            return {"logical_type": "name", "suggested_masking": "faker.name()"}
        else:
            # It's actually an ID, not a name
            return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}

    # Medical Data
    elif any(term in col_lower for term in ['doctor', 'physician', 'attending']) and not analyze_id_pattern(values or []):
        return {"logical_type": "name", "suggested_masking": "faker.name()"}
    elif any(term in col_lower for term in ['clinical_note', 'diagnosis', 'treatment_plan']):
        return {"logical_type": "text", "suggested_masking": "mask_clinical_notes"}
    elif any(term in col_lower for term in ['lab_result', 'test_value', 'measurement']):
        return {"logical_type": "numeric", "suggested_masking": "mask_lab_results"}
    elif any(term in col_lower for term in ['vital_sign', 'blood_pressure', 'heart_rate', 'temperature']):
        return {"logical_type": "numeric", "suggested_masking": "realistic_vital_signs"}

    # Dates
    elif any(term in col_lower for term in ['birth_date', 'dob', 'date_of_birth']):
        return {"logical_type": "date", "suggested_masking": "hipaa_age_group"}
    elif any(term in col_lower for term in ['appointment', 'visit_date', 'admission', 'discharge']):
        return {"logical_type": "date", "suggested_masking": "shift_days_random"}
    elif any(term in col_lower for term in ['collection_date', 'test_date', 'lab_date']):
        return {"logical_type": "date", "suggested_masking": "shift_consistent"}

    # Contact Information
    elif any(term in col_lower for term in ['phone', 'mobile', 'telephone']):
        return {"logical_type": "phone", "suggested_masking": "faker.phone_number()"}
    elif any(term in col_lower for term in ['email', 'e_mail']):
        return {"logical_type": "email", "suggested_masking": "faker.email()"}
    elif any(term in col_lower for term in ['address', 'street', 'city', 'state', 'zip']):
        return {"logical_type": "address", "suggested_masking": "faker.address()"}

    # Financial/Insurance
    elif any(term in col_lower for term in ['insurance', 'policy', 'coverage']):
        return {"logical_type": "identifier", "suggested_masking": "hash_sha256"}
    elif any(term in col_lower for term in ['ssn', 'social_security']):
        return {"logical_type": "identifier", "suggested_masking": "hash_sha256"}
    elif any(term in col_lower for term in ['billing']) and not analyze_id_pattern(values or []):
        return {"logical_type": "identifier", "suggested_masking": "tokenize"}

    # Medical Codes (usually don't need masking)
    elif any(term in col_lower for term in ['icd', 'cpt', 'procedure_code', 'diagnosis_code']):
        return {"logical_type": "code", "suggested_masking": "no masking"}
    elif any(term in col_lower for term in ['status', 'type', 'category', 'specimen_type']):
        return {"logical_type": "category", "suggested_masking": "no masking"}

    # Default based on SQL type - but ALWAYS check for ID patterns first
    if sql_type.lower() in ['varchar', 'text', 'char']:
        # Final check: does this look like an ID even if nothing else matched?
        if values and analyze_id_pattern(values):
            return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}
        else:
            return {"logical_type": "string", "suggested_masking": "scramble"}
    elif sql_type.lower() in ['int', 'integer', 'numeric', 'decimal', 'float']:
        return {"logical_type": "numeric", "suggested_masking": "add_noise_percentage"}
    elif sql_type.lower() in ['date', 'datetime', 'timestamp']:
        return {"logical_type": "date", "suggested_masking": "shift_days_random"}

    # Safe default
    return {"logical_type": "unknown", "suggested_masking": "no masking"}

def is_complex_case(col_name, values):
    """Determine if this is a complex case that benefits from GPT analysis"""
    col_lower = col_name.lower()

    # Simple cases that rules handle well
    simple_patterns = [
        'patient_id', 'mrn', 'first_name', 'last_name', 'phone', 'email',
        'birth_date', 'ssn', 'address', 'insurance', 'code', 'status'
    ]

    if any(pattern in col_lower for pattern in simple_patterns):
        return False

    # Complex cases that benefit from GPT
    if len(values) > 0:
        # Check if values are ambiguous or complex
        sample_value = str(values[0]) if values else ""
        if len(sample_value) > 50 or any(char in sample_value for char in ['{', '[', '<', 'http']):
            return True

    return True

# --- Enhanced Masking Plan ---
def get_masking_plan(schema, df, use_gpt=True, force_refresh=False):
    """
    Generate masking plan for given schema and data

    Args:
        schema: List of (column_name, sql_type) tuples
        df: DataFrame with sample data
        use_gpt: Whether to use GPT for complex cases
        force_refresh: If True, ignore cache and regenerate all suggestions
    """
    if force_refresh:
        cache = {}
    else:
        cache = load_cache()

    plan = []

    for col, sql_type in schema:
        key = f"{col}|{sql_type}"

        # Check cache first (unless force_refresh is True)
        if not force_refresh and key in cache:
            plan.append({**cache[key], "column": col})
            continue

        values = df[col].dropna().astype(str).tolist()[:5]

        # Get healthcare rule suggestion
        rule_suggestion = get_healthcare_fallback(col, sql_type, values)

        # Use GPT for complex cases or if explicitly requested
        if use_gpt and is_complex_case(col, values):
            try:
                gpt_result = ask_gpt_for_masking(col, sql_type, values)
                # Use GPT result if it seems reasonable, otherwise fall back to rules
                if gpt_result.get("suggested_masking") != "no masking":
                    result = gpt_result
                else:
                    result = rule_suggestion
            except Exception as e:
                print(f"⚠️ GPT failed for column `{col}`, using healthcare rules: {e}")
                result = rule_suggestion
        else:
            result = rule_suggestion

        # Cache the result
        cache[key] = result
        plan.append({**result, "column": col})

    save_cache(cache)
    return plan

# --- Text/String Masking Helper Functions ---
def scramble_text(val):
    return ''.join(random.sample(str(val), len(str(val)))) if isinstance(val, str) and len(str(val)) > 1 else val

def shuffle_words(val):
    """Shuffle words in a sentence"""
    if not isinstance(val, str):
        return val
    words = str(val).split()
    if len(words) > 1:
        random.shuffle(words)
        return ' '.join(words)
    return val

def mask_partial(val):
    """Mask middle characters (Jo***hn)"""
    if not isinstance(val, str) or len(str(val)) <= 2:
        return val
    s = str(val)
    if len(s) <= 4:
        return s[0] + '*' * (len(s) - 2) + s[-1]
    else:
        return s[:2] + '*' * (len(s) - 4) + s[-2:]

def mask_first_half(val):
    """Mask first half of string"""
    if not isinstance(val, str):
        return val
    s = str(val)
    mid = len(s) // 2
    return '*' * mid + s[mid:]

def mask_last_half(val):
    """Mask last half of string"""
    if not isinstance(val, str):
        return val
    s = str(val)
    mid = len(s) // 2
    return s[:mid] + '*' * (len(s) - mid)

def deterministic_hash(val, salt="default_salt"):
    """Generate deterministic hash - same input always produces same output"""
    return hashlib.sha256((str(val) + salt).encode()).hexdigest()[:12]

def random_string_same_length(val):
    """Generate random string of same length"""
    if not isinstance(val, str):
        return val
    import string
    length = len(str(val))
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def random_string_fixed_length(val, length=8):
    """Generate random string of fixed length"""
    import string
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def character_substitution(val):
    """Replace specific characters with similar ones"""
    if not isinstance(val, str):
        return val
    substitutions = {
        'a': '@', 'e': '3', 'i': '1', 'o': '0', 's': '$',
        'A': '@', 'E': '3', 'I': '1', 'O': '0', 'S': '$'
    }
    result = str(val)
    for old, new in substitutions.items():
        result = result.replace(old, new)
    return result

def encrypt_aes(val, key=None):
    """AES encryption (requires key for decryption)"""
    if key is None:
        key = Fernet.generate_key()
    f = Fernet(key)
    encrypted = f.encrypt(str(val).encode())
    return base64.urlsafe_b64encode(encrypted).decode()

# --- Numeric Masking Helper Functions ---
def add_noise_percentage(val, percentage=10):
    """Add random noise as percentage of original value"""
    if not isinstance(val, (int, float)):
        return val
    noise_factor = percentage / 100.0
    noise = random.uniform(-noise_factor, noise_factor) * val
    return val + noise

def add_noise_fixed(val, amount=10):
    """Add fixed amount of random noise"""
    if not isinstance(val, (int, float)):
        return val
    noise = random.uniform(-amount, amount)
    return val + noise

def multiply_by_factor(val, min_factor=0.8, max_factor=1.2):
    """Multiply by random factor"""
    if not isinstance(val, (int, float)):
        return val
    factor = random.uniform(min_factor, max_factor)
    return val * factor

def round_to_nearest(val, nearest=10):
    """Round to nearest specified value"""
    if not isinstance(val, (int, float)):
        return val
    return round(val / nearest) * nearest

def binning(val, bins=None):
    """Convert numeric value to categorical bins"""
    if not isinstance(val, (int, float)):
        return val
    if bins is None:
        bins = [0, 25, 50, 75, 100]

    for i in range(len(bins) - 1):
        if bins[i] <= val < bins[i + 1]:
            return f"{bins[i]}-{bins[i + 1] - 1}"
    return f"{bins[-1]}+"

def rank_transformation(val, reference_values):
    """Replace with rank/percentile"""
    if not isinstance(val, (int, float)) or not reference_values:
        return val
    sorted_vals = sorted([v for v in reference_values if isinstance(v, (int, float))])
    if not sorted_vals:
        return val

    rank = sum(1 for v in sorted_vals if v <= val)
    percentile = (rank / len(sorted_vals)) * 100
    return f"P{int(percentile)}"

def random_in_range(val, reference_values):
    """Generate random number within min-max range of reference values"""
    if not isinstance(val, (int, float)) or not reference_values:
        return val

    numeric_vals = [v for v in reference_values if isinstance(v, (int, float))]
    if not numeric_vals:
        return val

    min_val, max_val = min(numeric_vals), max(numeric_vals)
    if isinstance(val, int):
        return random.randint(int(min_val), int(max_val))
    else:
        return random.uniform(min_val, max_val)

def categorical_binning(val):
    """Convert numeric to Low/Medium/High categories"""
    if not isinstance(val, (int, float)):
        return val

    # This is a simple implementation - in practice, you'd want to determine
    # thresholds based on the data distribution
    if val < 33:
        return "Low"
    elif val < 67:
        return "Medium"
    else:
        return "High"

# --- Date/DateTime Masking Helper Functions ---
def shift_days_random(val, min_days=-30, max_days=30):
    """Shift date by random number of days"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_days, max_days)
    return val + timedelta(days=shift)

def shift_months_random(val, min_months=-6, max_months=6):
    """Shift date by random number of months"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_months, max_months)
    # Approximate month shift (30 days per month)
    return val + timedelta(days=shift * 30)

def shift_years_random(val, min_years=-2, max_years=2):
    """Shift date by random number of years"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_years, max_years)
    return val + timedelta(days=shift * 365)

def shift_days_fixed(val, days=30):
    """Shift date by fixed number of days"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val + timedelta(days=days)

def year_only(val):
    """Keep only year (set to January 1st)"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

def month_year_only(val):
    """Keep only month and year (set to 1st of month)"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

def quarter_year_only(val):
    """Keep only quarter and year"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    quarter = (val.month - 1) // 3 + 1
    return f"Q{quarter} {val.year}"

def day_of_week_only(val):
    """Keep only day of week"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.strftime("%A")

def season_only(val):
    """Keep only season"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    month = val.month
    if month in [12, 1, 2]:
        return "Winter"
    elif month in [3, 4, 5]:
        return "Spring"
    elif month in [6, 7, 8]:
        return "Summer"
    else:
        return "Fall"

def age_group(val):
    """Convert birth date to age group"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    today = datetime.now()
    age = today.year - val.year - ((today.month, today.day) < (val.month, val.day))

    if age < 18:
        return "Under 18"
    elif age < 25:
        return "18-24"
    elif age < 35:
        return "25-34"
    elif age < 45:
        return "35-44"
    elif age < 55:
        return "45-54"
    elif age < 65:
        return "55-64"
    else:
        return "65+"

def remove_time_component(val):
    """Keep only date part"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.date()

def remove_date_component(val):
    """Keep only time part"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.time()

def round_to_hour(val):
    """Round to nearest hour"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(minute=0, second=0, microsecond=0)

def round_to_day(val):
    """Round to nearest day"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(hour=0, minute=0, second=0, microsecond=0)

def randomize_time_component(val):
    """Keep date, randomize time"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    random_hour = random.randint(0, 23)
    random_minute = random.randint(0, 59)
    random_second = random.randint(0, 59)

    return val.replace(hour=random_hour, minute=random_minute, second=random_second, microsecond=0)

# --- Boolean Masking Helper Functions ---
def random_boolean(val):
    """Generate random boolean"""
    return random.choice([True, False])

def flip_percentage(val, percentage=10):
    """Flip boolean value X% of the time"""
    if not isinstance(val, bool):
        return val

    if random.randint(1, 100) <= percentage:
        return not val
    return val

def maintain_boolean_distribution(val, reference_values):
    """Maintain same true/false ratio as reference data"""
    if not reference_values:
        return random.choice([True, False])

    bool_vals = [v for v in reference_values if isinstance(v, bool)]
    if not bool_vals:
        return random.choice([True, False])

    true_ratio = sum(bool_vals) / len(bool_vals)
    return random.random() < true_ratio

# --- Special/Composite Data Type Masking Functions ---
def mask_json_fields(val, fields_to_mask=None):
    """Mask specific fields in JSON data"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, str):
            data = json.loads(val)
        else:
            data = val.copy()

        if fields_to_mask is None:
            fields_to_mask = ['name', 'email', 'phone', 'address']

        def mask_recursive(obj):
            if isinstance(obj, dict):
                for key in obj:
                    if key.lower() in [f.lower() for f in fields_to_mask]:
                        obj[key] = "[MASKED]"
                    elif isinstance(obj[key], (dict, list)):
                        mask_recursive(obj[key])
            elif isinstance(obj, list):
                for item in obj:
                    if isinstance(item, (dict, list)):
                        mask_recursive(item)

        mask_recursive(data)
        return json.dumps(data) if isinstance(val, str) else data

    except (json.JSONDecodeError, TypeError):
        return val

def scramble_json_values(val):
    """Scramble all string values in JSON"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, str):
            data = json.loads(val)
        else:
            data = val.copy()

        def scramble_recursive(obj):
            if isinstance(obj, dict):
                for key in obj:
                    if isinstance(obj[key], str):
                        obj[key] = scramble_text(obj[key])
                    elif isinstance(obj[key], (dict, list)):
                        scramble_recursive(obj[key])
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, str):
                        obj[i] = scramble_text(item)
                    elif isinstance(item, (dict, list)):
                        scramble_recursive(item)

        scramble_recursive(data)
        return json.dumps(data) if isinstance(val, str) else data

    except (json.JSONDecodeError, TypeError):
        return val

def hash_json_object(val):
    """Hash entire JSON object"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, dict):
            json_str = json.dumps(val, sort_keys=True)
        else:
            json_str = val

        return hashlib.sha256(json_str.encode()).hexdigest()

    except (TypeError, json.JSONDecodeError):
        return val

def shuffle_array(val):
    """Shuffle array elements"""
    if not isinstance(val, (list, str)):
        return val

    try:
        if isinstance(val, str):
            # Try to parse as JSON array
            data = json.loads(val)
            if isinstance(data, list):
                random.shuffle(data)
                return json.dumps(data)
            return val
        else:
            # Direct list
            shuffled = val.copy()
            random.shuffle(shuffled)
            return shuffled

    except (json.JSONDecodeError, TypeError):
        return val

def random_array_same_size(val):
    """Generate random array of same size"""
    if not isinstance(val, (list, str)):
        return val

    try:
        if isinstance(val, str):
            # Try to parse as JSON array
            data = json.loads(val)
            if isinstance(data, list):
                # Generate random array of same length
                random_data = [random.randint(1, 100) for _ in range(len(data))]
                return json.dumps(random_data)
            return val
        else:
            # Direct list
            return [random.randint(1, 100) for _ in range(len(val))]

    except (json.JSONDecodeError, TypeError):
        return val

# --- Advanced Utility Functions ---
def format_preserving_encrypt_digits(val, key=None):
    """Simple format-preserving encryption for numeric strings"""
    if not isinstance(val, str) or not val.isdigit():
        return val

    # Simple digit substitution (not cryptographically secure)
    digit_map = {
        '0': '7', '1': '4', '2': '9', '3': '1', '4': '8',
        '5': '2', '6': '0', '7': '5', '8': '3', '9': '6'
    }

    return ''.join(digit_map.get(d, d) for d in val)

def add_laplace_noise(val, sensitivity=1.0, epsilon=1.0):
    """Add Laplace noise for differential privacy"""
    if not isinstance(val, (int, float)):
        return val

    # Laplace noise: scale = sensitivity / epsilon
    scale = sensitivity / epsilon
    noise = np.random.laplace(0, scale)
    return val + noise

def add_gaussian_noise(val, sensitivity=1.0, epsilon=1.0, delta=1e-5):
    """Add Gaussian noise for differential privacy"""
    if not isinstance(val, (int, float)):
        return val

    # Gaussian noise for differential privacy
    sigma = (sensitivity * np.sqrt(2 * np.log(1.25 / delta))) / epsilon
    noise = np.random.normal(0, sigma)
    return val + noise

def generate_synthetic_similar(val, reference_values=None):
    """Generate synthetic data with similar properties"""
    if isinstance(val, str):
        # For strings, generate similar length and character types
        if not val:
            return ""

        import string
        char_types = []
        if any(c.isalpha() for c in val):
            char_types.extend(string.ascii_letters)
        if any(c.isdigit() for c in val):
            char_types.extend(string.digits)
        if any(c in string.punctuation for c in val):
            char_types.extend('.-_@')

        if not char_types:
            char_types = string.ascii_letters

        return ''.join(random.choices(char_types, k=len(val)))

    elif isinstance(val, (int, float)):
        # For numbers, generate within similar range
        if reference_values:
            numeric_refs = [v for v in reference_values if isinstance(v, (int, float))]
            if numeric_refs:
                min_val, max_val = min(numeric_refs), max(numeric_refs)
                if isinstance(val, int):
                    return random.randint(int(min_val), int(max_val))
                else:
                    return random.uniform(min_val, max_val)

        # Fallback: generate within ±50% of original value
        if isinstance(val, int):
            return random.randint(int(val * 0.5), int(val * 1.5))
        else:
            return random.uniform(val * 0.5, val * 1.5)

    return val

def preserve_data_relationships(df, columns, method="correlation"):
    """Preserve relationships between columns during masking"""
    if method == "correlation" and len(columns) >= 2:
        # Calculate correlation matrix
        numeric_cols = [col for col in columns if df[col].dtype in ['int64', 'float64']]
        if len(numeric_cols) >= 2:
            corr_matrix = df[numeric_cols].corr()
            return corr_matrix

    return None

def maintain_column_statistics(original_values, masked_values):
    """Check if statistical properties are maintained"""
    if not original_values or not masked_values:
        return {}

    orig_numeric = [v for v in original_values if isinstance(v, (int, float))]
    mask_numeric = [v for v in masked_values if isinstance(v, (int, float))]

    if not orig_numeric or not mask_numeric:
        return {}

    stats = {
        'original_mean': np.mean(orig_numeric),
        'masked_mean': np.mean(mask_numeric),
        'original_std': np.std(orig_numeric),
        'masked_std': np.std(mask_numeric),
        'mean_preserved': abs(np.mean(orig_numeric) - np.mean(mask_numeric)) < 0.1 * np.mean(orig_numeric),
        'std_preserved': abs(np.std(orig_numeric) - np.std(mask_numeric)) < 0.1 * np.std(orig_numeric)
    }

    return stats

# --- Healthcare-Specific Masking Functions ---
def generate_medical_record_number():
    """Generate realistic medical record number"""
    return f"MRN{random.randint(100000, 999999)}"

def generate_patient_id():
    """Generate realistic patient ID"""
    return f"PT{random.randint(10000, 99999)}"

def generate_insurance_number():
    """Generate realistic insurance number"""
    letters = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=3))
    numbers = ''.join(random.choices('**********', k=9))
    return f"{letters}{numbers}"

def format_preserving_id_mask(val):
    """
    Preserve ID format structure while masking content.
    Analyzes pattern and maintains character types in same positions.

    Examples:
    - CLMEA1A949D → XLMEA2B847F (letters stay letters, numbers stay numbers)
    - PAT468430B7 → QBU579541C8 (preserves exact pattern)
    """
    if not isinstance(val, str) or len(val) == 0:
        return val

    result = []
    for char in val:
        if char.isalpha():
            if char.isupper():
                result.append(random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ'))
            else:
                result.append(random.choice('abcdefghijklmnopqrstuvwxyz'))
        elif char.isdigit():
            result.append(random.choice('**********'))
        else:
            # Keep special characters (hyphens, underscores, etc.)
            result.append(char)

    return ''.join(result)

def smart_id_mask(val, reference_values=None):
    """
    Smart ID masking that analyzes the pattern across multiple values
    and preserves common prefixes/suffixes while masking variable parts.
    """
    if not isinstance(val, str) or len(val) == 0:
        return val

    # If we have reference values, analyze the pattern
    if reference_values and len(reference_values) > 1:
        # Find common patterns across all values
        common_prefix = find_common_prefix([str(v) for v in reference_values if isinstance(v, str)])
        common_suffix = find_common_suffix([str(v) for v in reference_values if isinstance(v, str)])

        # Preserve common parts, mask variable parts
        if len(common_prefix) > 0 or len(common_suffix) > 0:
            prefix_len = len(common_prefix)
            suffix_len = len(common_suffix)

            if suffix_len > 0:
                middle_part = val[prefix_len:-suffix_len]
                masked_middle = format_preserving_id_mask(middle_part)
                return common_prefix + masked_middle + common_suffix
            else:
                middle_part = val[prefix_len:]
                masked_middle = format_preserving_id_mask(middle_part)
                return common_prefix + masked_middle

    # Fallback to simple format-preserving masking
    return format_preserving_id_mask(val)

def find_common_prefix(strings):
    """Find common prefix across multiple strings"""
    if not strings:
        return ""

    prefix = strings[0]
    for s in strings[1:]:
        while prefix and not s.startswith(prefix):
            prefix = prefix[:-1]
    return prefix

def find_common_suffix(strings):
    """Find common suffix across multiple strings"""
    if not strings:
        return ""

    # Reverse strings to find common prefix of reversed strings
    reversed_strings = [s[::-1] for s in strings]
    common_reversed_prefix = find_common_prefix(reversed_strings)
    return common_reversed_prefix[::-1]

def analyze_id_pattern(values):
    """
    Analyze ID pattern to determine if it's a structured ID that should use
    format-preserving masking instead of name generation.
    Enhanced to catch patterns like CLMEA1A949D, PATFD81BF08, etc.
    """
    if not values or len(values) < 1:  # Reduced from 2 to 1 for single-value detection
        return False

    string_values = [str(v) for v in values if isinstance(v, str) and len(str(v)) > 3]
    if len(string_values) < 1:  # Reduced from 2 to 1
        return False

    # Check if values look like IDs (mix of letters and numbers, consistent length)
    lengths = [len(v) for v in string_values]
    avg_length = sum(lengths) / len(lengths)

    # IDs typically have consistent length (expanded range)
    if not (4 <= avg_length <= 25):  # Expanded from 6-20 to 4-25
        return False

    # Check if most values have similar length (within 3 characters - more lenient)
    if len(string_values) > 1:
        length_variance = max(lengths) - min(lengths)
        if length_variance > 3:  # Increased from 2 to 3
            return False

    # Enhanced pattern detection
    id_indicators = 0
    total_checks = 0

    for val in string_values[:5]:  # Check first 5 values
        total_checks += 1

        # Check 1: Mix of letters and numbers (classic ID pattern)
        has_letters = any(c.isalpha() for c in val)
        has_numbers = any(c.isdigit() for c in val)
        if has_letters and has_numbers:
            id_indicators += 1
            continue

        # Check 2: All uppercase letters with numbers (like your data)
        if val.isupper() and any(c.isdigit() for c in val):
            id_indicators += 1
            continue

        # Check 3: Starts with common ID prefixes
        id_prefixes = ['CLM', 'PAT', 'PROV', 'MRN', 'ID', 'ACC', 'MEM', 'INS']
        if any(val.upper().startswith(prefix) for prefix in id_prefixes):
            id_indicators += 1
            continue

        # Check 4: Contains common ID patterns (letters followed by numbers)
        import re
        if re.match(r'^[A-Z]{2,5}[A-Z0-9]{4,}$', val.upper()):
            id_indicators += 1
            continue

        # Check 5: Hex-like patterns (common in hashed IDs)
        if re.match(r'^[A-F0-9]{8,}$', val.upper()):
            id_indicators += 1
            continue

    # If 60% or more of values look like IDs, classify as ID pattern
    # Lowered threshold for better detection
    return id_indicators >= max(1, total_checks * 0.5)  # 50% threshold instead of 60%

def mask_clinical_notes(val, sensitivity_level="high"):
    """Mask clinical notes based on sensitivity level"""
    if not isinstance(val, str):
        return val

    if sensitivity_level == "high":
        return "[CLINICAL NOTES REDACTED FOR PRIVACY]"
    elif sensitivity_level == "medium":
        # Keep structure but mask sensitive words
        sensitive_words = ['patient', 'diagnosis', 'treatment', 'medication', 'condition']
        result = str(val)
        for word in sensitive_words:
            result = result.replace(word.lower(), '[REDACTED]')
            result = result.replace(word.capitalize(), '[REDACTED]')
        return result
    else:  # low
        return scramble_text(val)

def generate_realistic_vital_signs(val, vital_type="general"):
    """Generate realistic vital signs based on type"""
    if not isinstance(val, (int, float)):
        return val

    # Realistic ranges for different vital signs
    vital_ranges = {
        'blood_pressure_systolic': (90, 180),
        'blood_pressure_diastolic': (60, 120),
        'heart_rate': (60, 100),
        'temperature': (97.0, 99.5),
        'respiratory_rate': (12, 20),
        'oxygen_saturation': (95, 100),
        'general': (int(val * 0.8), int(val * 1.2))
    }

    min_val, max_val = vital_ranges.get(vital_type, vital_ranges['general'])

    if isinstance(val, int):
        return random.randint(min_val, max_val)
    else:
        return round(random.uniform(min_val, max_val), 1)

def mask_lab_results(val, result_type="normal_range"):
    """Mask lab results while maintaining clinical relevance"""
    if not isinstance(val, (int, float)):
        return val

    if result_type == "normal_range":
        # Keep within normal ranges but add noise
        noise = random.uniform(-0.05, 0.05) * val
        return round(val + noise, 2)
    elif result_type == "categorical":
        # Convert to categorical
        if val < 33:
            return "Low"
        elif val < 67:
            return "Normal"
        else:
            return "High"
    else:
        return add_noise_percentage(val, 15)  # 15% noise for lab values

def generate_hipaa_compliant_age(birth_date):
    """Convert birth date to HIPAA-compliant age group"""
    try:
        if isinstance(birth_date, str):
            birth_date = pd.to_datetime(birth_date)

        today = datetime.now()
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))

        # HIPAA Safe Harbor age groups
        if age < 18:
            return "Under 18"
        elif age <= 24:
            return "18-24"
        elif age <= 34:
            return "25-34"
        elif age <= 44:
            return "35-44"
        elif age <= 54:
            return "45-54"
        elif age <= 64:
            return "55-64"
        elif age <= 74:
            return "65-74"
        elif age <= 84:
            return "75-84"
        else:
            return "85+"
    except:
        return "Unknown"

def preserve_medical_relationships(df, patient_id_col, date_cols):
    """Preserve relationships in medical data during masking"""
    # Create consistent shifts for each patient
    patient_shifts = {}

    for patient_id in df[patient_id_col].unique():
        if pd.notna(patient_id):
            # Same shift for all dates for this patient
            patient_shifts[patient_id] = random.randint(-30, 30)

    return patient_shifts

def mask_column(val, method, reference_values=None):
    if pd.isnull(val):
        return val

    try:
        # === FAKER METHODS ===
        if method.startswith("faker."):
            method_name = method.replace("faker.", "").replace("()", "")
            faker_fn = getattr(fake, method_name, None)
            if callable(faker_fn):
                return faker_fn()

        # === TEXT/STRING MASKING ===
        elif method == "hash_sha256":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "hash_md5":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "hash_sha1":
            return hashlib.sha1(str(val).encode()).hexdigest()
        elif method == "scramble":
            return scramble_text(val)
        elif method == "shuffle_words":
            return shuffle_words(val)
        elif method == "mask_out":
            return '*' * len(str(val))
        elif method == "mask_partial":
            return mask_partial(val)
        elif method == "mask_first_half":
            return mask_first_half(val)
        elif method == "mask_last_half":
            return mask_last_half(val)
        elif method == "redact":
            return "[REDACTED]"
        elif method == "nullify":
            return None
        elif method == "tokenize":
            return str(uuid.uuid4())[:12]
        elif method == "encrypt_aes":
            return encrypt_aes(val)
        elif method == "deterministic_hash":
            return deterministic_hash(val)
        elif method == "format_preserving_id":
            return smart_id_mask(val, reference_values)
        elif method == "random_string_same_length":
            return random_string_same_length(val)
        elif method == "random_string_fixed_length":
            return random_string_fixed_length(val)
        elif method == "substitute_from_list" and reference_values:
            return random.choice(reference_values)
        elif method == "character_substitution":
            return character_substitution(val)

        # === NUMERIC MASKING ===
        elif method == "add_noise_percentage":
            return add_noise_percentage(val)
        elif method == "add_noise_fixed":
            return add_noise_fixed(val)
        elif method == "multiply_by_factor":
            return multiply_by_factor(val)
        elif method == "round_to_nearest_10":
            return round_to_nearest(val, 10)
        elif method == "round_to_nearest_100":
            return round_to_nearest(val, 100)
        elif method == "round_to_nearest_1000":
            return round_to_nearest(val, 1000)
        elif method == "binning":
            return binning(val)
        elif method == "rank_transformation" and reference_values:
            return rank_transformation(val, reference_values)
        elif method == "random_in_range" and reference_values:
            return random_in_range(val, reference_values)
        elif method == "random_in_percentile_range" and reference_values:
            return random_in_range(val, reference_values)  # Same implementation for now
        elif method == "categorical_binning":
            return categorical_binning(val)
        elif method == "maintain_distribution" and reference_values:
            return random_in_range(val, reference_values)  # Simplified implementation
        elif method == "variance":
            if isinstance(val, (int, float)):
                noise = random.uniform(-0.1, 0.1) * val
                return val + noise

        # === DATE/DATETIME MASKING ===
        elif method == "shift_days_random":
            return shift_days_random(val)
        elif method == "shift_months_random":
            return shift_months_random(val)
        elif method == "shift_years_random":
            return shift_years_random(val)
        elif method == "shift_days_fixed":
            return shift_days_fixed(val)
        elif method == "shift_consistent":
            return shift_days_fixed(val, 30)  # Consistent 30-day shift
        elif method == "year_only":
            return year_only(val)
        elif method == "month_year_only":
            return month_year_only(val)
        elif method == "quarter_year_only":
            return quarter_year_only(val)
        elif method == "day_of_week_only":
            return day_of_week_only(val)
        elif method == "season_only":
            return season_only(val)
        elif method == "age_group":
            return age_group(val)
        elif method == "remove_time_component":
            return remove_time_component(val)
        elif method == "remove_date_component":
            return remove_date_component(val)
        elif method == "round_to_hour":
            return round_to_hour(val)
        elif method == "round_to_day":
            return round_to_day(val)
        elif method == "randomize_time_component":
            return randomize_time_component(val)

        # === BOOLEAN MASKING ===
        elif method == "random_boolean":
            return random_boolean(val)
        elif method == "flip_percentage":
            return flip_percentage(val)
        elif method == "maintain_boolean_distribution" and reference_values:
            return maintain_boolean_distribution(val, reference_values)

        # === SPECIAL/COMPOSITE DATA TYPES ===
        elif method == "mask_json_fields":
            return mask_json_fields(val)
        elif method == "scramble_json_values":
            return scramble_json_values(val)
        elif method == "hash_json_object":
            return hash_json_object(val)
        elif method == "shuffle_array":
            return shuffle_array(val)
        elif method == "random_array_same_size":
            return random_array_same_size(val)

        # === ADVANCED TECHNIQUES ===
        elif method == "format_preserving_encrypt":
            return format_preserving_encrypt_digits(val)
        elif method == "laplace_noise":
            return add_laplace_noise(val)
        elif method == "gaussian_noise":
            return add_gaussian_noise(val)
        elif method == "synthetic_similar" and reference_values:
            return generate_synthetic_similar(val, reference_values)

        # === HEALTHCARE-SPECIFIC METHODS ===
        elif method == "generate_mrn":
            return generate_medical_record_number()
        elif method == "generate_patient_id":
            return generate_patient_id()
        elif method == "generate_insurance_number":
            return generate_insurance_number()
        elif method == "mask_clinical_notes":
            return mask_clinical_notes(val)
        elif method == "realistic_vital_signs":
            return generate_realistic_vital_signs(val)
        elif method == "mask_lab_results":
            return mask_lab_results(val)
        elif method == "hipaa_age_group":
            return generate_hipaa_compliant_age(val)

        # === LEGACY/BACKWARD COMPATIBILITY ===
        elif method == "hash":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "encrypt":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "substitute" and reference_values:
            return random.choice(reference_values)
        elif "shift date" in method.lower():
            shift = random.randint(-5, 5)
            return val + timedelta(days=shift)

        # === NO MASKING ===
        elif method == "no masking":
            return val

    except Exception as e:
        print(f"⚠️ Error masking value `{val}` with method `{method}`: {e}")
        return val

    return val  # fallback

def apply_masking(df, masking_plan):
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']

        # Special handling for shuffle - operates on entire column
        if method == "shuffle":
            df[col] = df[col].sample(frac=1).reset_index(drop=True)
        else:
            # Determine if method needs reference values
            methods_needing_reference = [
                "substitute", "substitute_from_list", "rank_transformation",
                "random_in_range", "random_in_percentile_range",
                "maintain_distribution", "maintain_boolean_distribution"
            ]

            reference_values = None
            if any(ref_method in method for ref_method in methods_needing_reference):
                reference_values = df[col].dropna().tolist()

            # Apply masking to each value in the column
            df[col] = df[col].apply(lambda v: mask_column(v, method, reference_values))

    return df

def apply_selective_masking(df, masking_plan, row_selection_mode="🔄 Mask All Rows",
                          start_row=0, end_row=None, num_rows=None, random_seed=None,
                          preserve_relationships=True, include_original=False,
                          add_mask_indicators=False):
    """
    Enhanced masking function with selective row and column masking

    Args:
        df: Original DataFrame
        masking_plan: List of column masking configurations
        row_selection_mode: How to select rows for masking
        start_row: Starting row index for range selection
        end_row: Ending row index for range selection
        num_rows: Number of rows to mask
        random_seed: Seed for random row selection
        preserve_relationships: Whether to maintain consistent masking
        include_original: Whether to include original columns
        add_mask_indicators: Whether to add masking indicator columns

    Returns:
        DataFrame with selective masking applied
    """
    import random
    import numpy as np
    import pandas as pd

    # Create a copy of the original dataframe
    result_df = df.copy()

    # Determine which rows to mask
    total_rows = len(df)
    if end_row is None:
        end_row = total_rows

    if row_selection_mode == "🔄 Mask All Rows":
        rows_to_mask = list(range(total_rows))
    elif row_selection_mode == "🔢 Mask Specific Number of Rows":
        rows_to_mask = list(range(min(num_rows, total_rows)))
    elif row_selection_mode == "📍 Mask Specific Row Range":
        rows_to_mask = list(range(start_row, min(end_row, total_rows)))
    elif row_selection_mode == "🎲 Mask Random Sample":
        if random_seed is not None:
            random.seed(random_seed)
            np.random.seed(random_seed)
        rows_to_mask = random.sample(range(total_rows), min(num_rows, total_rows))
    else:
        rows_to_mask = list(range(total_rows))

    # Create masking indicators if requested
    if add_mask_indicators:
        for col_plan in masking_plan:
            col = col_plan['column']
            method = col_plan['suggested_masking']
            indicator_col = f"{col}_mask_method"
            result_df[indicator_col] = "no masking"
            result_df.loc[rows_to_mask, indicator_col] = method

    # Apply masking to selected columns and rows
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']

        # Skip if no masking requested
        if method == "no masking":
            continue

        # Include original column if requested
        if include_original:
            result_df[f"{col}_original"] = result_df[col].copy()

        # Special handling for shuffle - operates on entire column subset
        if method == "shuffle":
            # Only shuffle the selected rows
            masked_values = result_df.loc[rows_to_mask, col].sample(frac=1).reset_index(drop=True)
            result_df.loc[rows_to_mask, col] = masked_values
        else:
            # Determine if method needs reference values
            methods_needing_reference = [
                "substitute", "substitute_from_list", "rank_transformation",
                "random_in_range", "random_in_percentile_range",
                "maintain_distribution", "maintain_boolean_distribution"
            ]

            reference_values = None
            if any(ref_method in method for ref_method in methods_needing_reference):
                reference_values = df[col].dropna().tolist()

            # Create a consistent mapping if preserving relationships
            if preserve_relationships:
                unique_values = df[col].dropna().unique()
                value_mapping = {}
                for unique_val in unique_values:
                    value_mapping[unique_val] = mask_column(unique_val, method, reference_values)

                # Apply consistent mapping to selected rows
                for idx in rows_to_mask:
                    if idx < len(result_df) and not pd.isna(result_df.loc[idx, col]):
                        original_value = result_df.loc[idx, col]
                        if original_value in value_mapping:
                            result_df.loc[idx, col] = value_mapping[original_value]
                        else:
                            result_df.loc[idx, col] = mask_column(original_value, method, reference_values)
            else:
                # Apply masking independently to each selected row
                for idx in rows_to_mask:
                    if idx < len(result_df) and not pd.isna(result_df.loc[idx, col]):
                        result_df.loc[idx, col] = mask_column(result_df.loc[idx, col], method, reference_values)

    return result_df

def generate_masking_report(original_df, masked_df, masking_plan, session_state):
    """Generate a detailed masking report"""
    from datetime import datetime

    report_lines = []
    report_lines.append("=" * 60)
    report_lines.append("🏥 HEALTHCARE DATA MASKING REPORT")
    report_lines.append("=" * 60)
    report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")

    # Summary statistics
    report_lines.append("📊 MASKING SUMMARY")
    report_lines.append("-" * 30)
    report_lines.append(f"Original rows: {len(original_df)}")
    report_lines.append(f"Processed rows: {len(masked_df)}")
    report_lines.append(f"Total columns: {len(original_df.columns)}")

    masked_columns = len([p for p in masking_plan if p["suggested_masking"] != "no masking"])
    report_lines.append(f"Masked columns: {masked_columns}")
    report_lines.append(f"Unchanged columns: {len(original_df.columns) - masked_columns}")
    report_lines.append("")

    # Row selection details
    row_selection_mode = session_state.get("row_selection_mode", "🔄 Mask All Rows")
    report_lines.append("📋 ROW SELECTION")
    report_lines.append("-" * 20)
    report_lines.append(f"Selection mode: {row_selection_mode}")

    if row_selection_mode == "📍 Mask Specific Row Range":
        start_row = session_state.get("start_row", 0)
        end_row = session_state.get("end_row", len(original_df))
        report_lines.append(f"Row range: {start_row} to {end_row}")
    elif row_selection_mode == "🔢 Mask Specific Number of Rows":
        num_rows = session_state.get("num_rows", len(original_df))
        report_lines.append(f"Number of rows: {num_rows}")
    elif row_selection_mode == "🎲 Mask Random Sample":
        num_rows = session_state.get("num_rows", len(original_df))
        random_seed = session_state.get("random_seed", None)
        report_lines.append(f"Random sample size: {num_rows}")
        if random_seed is not None:
            report_lines.append(f"Random seed: {random_seed}")

    report_lines.append("")

    # Column masking details
    report_lines.append("🧪 COLUMN MASKING METHODS")
    report_lines.append("-" * 30)
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']
        logical_type = col_plan.get('logical_type', 'unknown')

        status = "MASKED" if method != "no masking" else "UNCHANGED"
        report_lines.append(f"{col:<20} | {method:<25} | {logical_type:<15} | {status}")

    report_lines.append("")

    # Advanced options
    report_lines.append("🎛️ ADVANCED OPTIONS")
    report_lines.append("-" * 20)
    preserve_relationships = session_state.get("preserve_relationships", True)
    include_original = session_state.get("include_original", False)
    add_mask_indicators = session_state.get("add_mask_indicators", False)

    report_lines.append(f"Preserve relationships: {preserve_relationships}")
    report_lines.append(f"Include original columns: {include_original}")
    report_lines.append(f"Add masking indicators: {add_mask_indicators}")
    report_lines.append("")

    # Data samples (first few rows)
    report_lines.append("📋 DATA SAMPLE (First 3 rows)")
    report_lines.append("-" * 35)

    # Show original vs masked for first few columns and rows
    sample_cols = list(original_df.columns)[:3]  # First 3 columns
    sample_rows = min(3, len(original_df))  # First 3 rows

    for i in range(sample_rows):
        report_lines.append(f"Row {i}:")
        for col in sample_cols:
            if col in original_df.columns and col in masked_df.columns:
                orig_val = str(original_df.iloc[i][col])[:20]  # Truncate long values
                masked_val = str(masked_df.iloc[i][col])[:20]
                report_lines.append(f"  {col}: {orig_val} → {masked_val}")
        report_lines.append("")

    # HIPAA compliance note
    report_lines.append("🔒 HIPAA COMPLIANCE")
    report_lines.append("-" * 20)
    report_lines.append("This masking process follows HIPAA Safe Harbor guidelines:")
    report_lines.append("• All direct identifiers have been removed or masked")
    report_lines.append("• Healthcare data relationships preserved for functionality")
    report_lines.append("• No re-identification risk from masked data")
    report_lines.append("• Safe for development, testing, and training environments")
    report_lines.append("")

    report_lines.append("=" * 60)
    report_lines.append("End of Report")
    report_lines.append("=" * 60)

    return "\n".join(report_lines)

def apply_cross_table_masking(tables_data, masking_plans, relationship_mappings=None,
                            preserve_relationships=True, **kwargs):
    """
    Apply masking across multiple related tables while preserving referential integrity

    Args:
        tables_data: Dict of {table_name: DataFrame}
        masking_plans: Dict of {table_name: masking_plan}
        relationship_mappings: Dict defining relationships between tables
        preserve_relationships: Whether to maintain consistent masking across tables
        **kwargs: Additional arguments for selective masking

    Returns:
        Dict of {table_name: masked_DataFrame}
    """
    import pandas as pd

    # Auto-detect relationships if not provided
    if relationship_mappings is None:
        relationship_mappings = auto_detect_relationships(tables_data)

    # Create global value mappings for consistent masking across tables
    global_mappings = {}

    # First pass: identify all columns that need consistent masking
    if preserve_relationships:
        for table_name, df in tables_data.items():
            plan = masking_plans.get(table_name, [])
            for col_plan in plan:
                col = col_plan['column']
                method = col_plan['suggested_masking']

                # Skip if no masking
                if method == "no masking":
                    continue

                # Check if this column appears in relationship mappings
                for rel in relationship_mappings:
                    if (rel['table1'] == table_name and rel['column1'] == col) or \
                       (rel['table2'] == table_name and rel['column2'] == col):

                        # Create consistent mapping for this relationship
                        rel_key = f"{rel['table1']}.{rel['column1']}-{rel['table2']}.{rel['column2']}"
                        if rel_key not in global_mappings:
                            # Collect all unique values from both tables
                            all_values = set()

                            # Add values from table1
                            if rel['table1'] in tables_data and rel['column1'] in tables_data[rel['table1']].columns:
                                all_values.update(tables_data[rel['table1']][rel['column1']].dropna().unique())

                            # Add values from table2
                            if rel['table2'] in tables_data and rel['column2'] in tables_data[rel['table2']].columns:
                                all_values.update(tables_data[rel['table2']][rel['column2']].dropna().unique())

                            # Create consistent mapping for all values
                            value_mapping = {}
                            reference_values = list(all_values)

                            for unique_val in all_values:
                                if pd.notna(unique_val):
                                    value_mapping[unique_val] = mask_column(unique_val, method, reference_values)

                            global_mappings[rel_key] = {
                                'mapping': value_mapping,
                                'method': method,
                                'table1': rel['table1'],
                                'column1': rel['column1'],
                                'table2': rel['table2'],
                                'column2': rel['column2']
                            }

    # Second pass: apply masking to each table
    masked_tables = {}

    for table_name, df in tables_data.items():
        plan = masking_plans.get(table_name, [])

        # Apply selective masking with global mappings
        masked_df = apply_selective_masking_with_global_mappings(
            df, plan, global_mappings, table_name, **kwargs
        )

        masked_tables[table_name] = masked_df

    return masked_tables, global_mappings

def auto_detect_relationships(tables_data):
    """
    Auto-detect relationships between tables based on common column names
    """
    relationships = []
    table_names = list(tables_data.keys())

    # Compare each pair of tables
    for i, table1 in enumerate(table_names):
        for j, table2 in enumerate(table_names):
            if i >= j:  # Avoid duplicates and self-comparison
                continue

            df1 = tables_data[table1]
            df2 = tables_data[table2]

            # Find common column names (potential foreign keys)
            common_columns = set(df1.columns) & set(df2.columns)

            for col in common_columns:
                # Check if this looks like a key column
                if is_likely_key_column(col, df1[col], df2[col]):
                    relationships.append({
                        'table1': table1,
                        'column1': col,
                        'table2': table2,
                        'column2': col,
                        'relationship_type': 'foreign_key'
                    })

    return relationships

def is_likely_key_column(col_name, series1, series2):
    """
    Determine if a column is likely a key column based on name and data patterns
    """
    col_lower = col_name.lower()

    # Check column name patterns
    key_indicators = ['id', 'key', 'code', 'number', 'ref']
    if any(indicator in col_lower for indicator in key_indicators):
        # Check if values look like keys (not too many unique values relative to total)
        total_values1 = len(series1.dropna())
        unique_values1 = len(series1.dropna().unique())

        total_values2 = len(series2.dropna())
        unique_values2 = len(series2.dropna().unique())

        # If both series have reasonable uniqueness ratios, likely a key
        if (total_values1 > 0 and unique_values1 / total_values1 > 0.1 and
            total_values2 > 0 and unique_values2 / total_values2 > 0.1):

            # Check if there are common values between tables
            common_values = set(series1.dropna()) & set(series2.dropna())
            if len(common_values) > 0:
                return True

    return False

def apply_selective_masking_with_global_mappings(df, masking_plan, global_mappings,
                                               table_name, **kwargs):
    """
    Apply selective masking using global mappings for consistent cross-table masking
    """
    import pandas as pd

    # Create a copy to work with
    result_df = df.copy()

    # Apply masking column by column
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']

        # Skip if no masking
        if method == "no masking":
            continue

        # Check if this column is part of a relationship
        is_relationship_column = False
        relationship_mapping = None

        for mapping_info in global_mappings.values():
            if ((table_name == mapping_info['table1'] and col == mapping_info['column1']) or
                (table_name == mapping_info['table2'] and col == mapping_info['column2'])):
                is_relationship_column = True
                relationship_mapping = mapping_info['mapping']
                break

        if is_relationship_column and relationship_mapping:
            # Use the global mapping for consistency
            for idx in result_df.index:
                original_value = result_df.loc[idx, col]
                if pd.notna(original_value) and original_value in relationship_mapping:
                    result_df.loc[idx, col] = relationship_mapping[original_value]
        else:
            # Apply regular masking
            reference_values = df[col].dropna().tolist()
            for idx in result_df.index:
                if pd.notna(result_df.loc[idx, col]):
                    result_df.loc[idx, col] = mask_column(result_df.loc[idx, col], method, reference_values)

    return result_df

# === OUTPUT FILE GENERATION FUNCTIONS ===

def generate_csv_output(df, table_name="table"):
    """Generate CSV format output"""
    return df.to_csv(index=False)

def generate_json_output(df, table_name="table"):
    """Generate JSON format output"""
    return df.to_json(orient='records', indent=2)

def generate_sql_insert_statements(df, table_name="table"):
    """Generate SQL INSERT statements"""
    if df.empty:
        return f"-- No data to insert for table {table_name}\n"

    # Create INSERT statements
    sql_statements = []

    # Add table comment
    sql_statements.append(f"-- INSERT statements for table: {table_name}")
    sql_statements.append(f"-- Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
    sql_statements.append(f"-- Total rows: {len(df)}")
    sql_statements.append("")

    # Get column names
    columns = df.columns.tolist()
    columns_str = ", ".join([f'"{col}"' for col in columns])

    # Generate INSERT statements (batch approach for better performance)
    batch_size = 100
    for i in range(0, len(df), batch_size):
        batch_df = df.iloc[i:i+batch_size]

        # Create VALUES clause for this batch
        values_list = []
        for _, row in batch_df.iterrows():
            values = []
            for col in columns:
                value = row[col]
                if pd.isna(value):
                    values.append("NULL")
                elif isinstance(value, str):
                    # Escape single quotes in strings
                    escaped_value = str(value).replace("'", "''")
                    values.append(f"'{escaped_value}'")
                elif isinstance(value, (int, float)):
                    values.append(str(value))
                else:
                    # Convert other types to string
                    escaped_value = str(value).replace("'", "''")
                    values.append(f"'{escaped_value}'")
            values_list.append(f"({', '.join(values)})")

        # Create INSERT statement for this batch
        values_clause = ",\n    ".join(values_list)
        insert_statement = f"""INSERT INTO "{table_name}" ({columns_str})
VALUES
    {values_clause};"""

        sql_statements.append(insert_statement)
        sql_statements.append("")  # Empty line between batches

    return "\n".join(sql_statements)

def generate_multi_table_outputs(masked_tables, output_format="csv"):
    """Generate outputs for multiple tables"""
    outputs = {}

    for table_name, masked_df in masked_tables.items():
        if output_format == "csv":
            outputs[table_name] = generate_csv_output(masked_df, table_name)
        elif output_format == "json":
            outputs[table_name] = generate_json_output(masked_df, table_name)
        elif output_format == "sql":
            outputs[table_name] = generate_sql_insert_statements(masked_df, table_name)

    return outputs

def create_combined_output(masked_tables, output_format="csv"):
    """Create a single combined file with all tables"""
    combined_parts = []

    if output_format == "csv":
        for table_name, masked_df in masked_tables.items():
            combined_parts.append(f"# Table: {table_name}")
            combined_parts.append(f"# Rows: {len(masked_df)}")
            combined_parts.append(f"# Columns: {len(masked_df.columns)}")
            combined_parts.append("")
            combined_parts.append(masked_df.to_csv(index=False))
            combined_parts.append("")
            combined_parts.append("=" * 50)
            combined_parts.append("")

    elif output_format == "json":
        combined_data = {}
        for table_name, masked_df in masked_tables.items():
            combined_data[table_name] = {
                "metadata": {
                    "table_name": table_name,
                    "row_count": len(masked_df),
                    "column_count": len(masked_df.columns),
                    "columns": masked_df.columns.tolist()
                },
                "data": masked_df.to_dict('records')
            }
        return json.dumps(combined_data, indent=2)

    elif output_format == "sql":
        for table_name, masked_df in masked_tables.items():
            combined_parts.append(f"-- ========================================")
            combined_parts.append(f"-- Table: {table_name}")
            combined_parts.append(f"-- ========================================")
            combined_parts.append("")
            combined_parts.append(generate_sql_insert_statements(masked_df, table_name))
            combined_parts.append("")

    return "\n".join(combined_parts)
