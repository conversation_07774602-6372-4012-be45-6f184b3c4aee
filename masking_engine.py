import os
import json
import psycopg2
import pandas as pd
from dotenv import load_dotenv
from datetime import timedelta, datetime
import random
import hashlib
import uuid
import re
import numpy as np
from faker import Faker
from openai import OpenAI
from cryptography.fernet import Fernet
import base64

load_dotenv()
fake = Faker()

# OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# --- Connect to PostgreSQL ---
def get_connection():
    return psycopg2.connect(
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_NAME"),
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD")
    )

# --- Get Schema + Sample Data ---
def get_schema_and_samples(table_name, limit=5):
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute(f"""
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = '{table_name}';
    """)
    schema = cursor.fetchall()

    df = pd.read_sql(f"SELECT * FROM {table_name} LIMIT {limit};", conn)

    cursor.close()
    conn.close()
    return schema, df

# --- Cache File ---
CACHE_FILE = "masking_gpt_cache.json"

def load_cache():
    return json.load(open(CACHE_FILE)) if os.path.exists(CACHE_FILE) else {}

def save_cache(cache):
    with open(CACHE_FILE, "w") as f:
        json.dump(cache, f, indent=2)

# --- GPT Call with Safe JSON Decoding ---
def ask_gpt_for_masking(col_name, sql_type, values):
    prompt = f"""Healthcare data masking expert. Analyze: {col_name} ({sql_type}) with samples: {values[:3]}

RULES:
- Patient/doctor names → "faker.name()"
- Medical IDs/MRNs → "deterministic_hash"
- Birth dates → "hipaa_age_group"
- Clinical notes → "mask_clinical_notes"
- Lab values → "mask_lab_results"
- Insurance → "hash_sha256"
- Dates → "shift_days_random"
- Phone → "faker.phone_number()"
- Email → "faker.email()"
- Codes/Status → "no masking"

JSON only:
{{"logical_type": "string", "suggested_masking": "faker.name()"}}"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2
        )
        raw = response.choices[0].message.content.strip()
        print(f"\n🧠 GPT Response for column `{col_name}`:\n{raw}\n")
        return json.loads(raw)

    except Exception as e:
        print(f"⚠️ Error parsing GPT response for column `{col_name}`: {e}")
        return {
            "logical_type": "unknown",
            "suggested_masking": "no masking"
        }

# --- Healthcare-Specific Rules ---
def get_healthcare_fallback(col_name, sql_type, values=None):
    """Healthcare-specific fallback rules for common medical data"""
    col_lower = col_name.lower()

    # Check if this looks like a structured ID that should preserve format
    if values and analyze_id_pattern(values):
        # For ID-like columns, use format-preserving masking
        if any(term in col_lower for term in ['id', 'number', 'code', 'claim', 'member', 'account']):
            return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}

    # Patient Identifiers
    if any(term in col_lower for term in ['patient_id', 'mrn', 'medical_record']):
        # Check if it's a structured ID or simple hash
        if values and analyze_id_pattern(values):
            return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}
        else:
            return {"logical_type": "identifier", "suggested_masking": "deterministic_hash"}
    elif any(term in col_lower for term in ['patient_name', 'first_name', 'last_name']):
        return {"logical_type": "name", "suggested_masking": "faker.name()"}

    # Medical Data
    elif any(term in col_lower for term in ['doctor', 'physician', 'provider', 'attending']):
        return {"logical_type": "name", "suggested_masking": "faker.name()"}
    elif any(term in col_lower for term in ['clinical_note', 'diagnosis', 'treatment_plan']):
        return {"logical_type": "text", "suggested_masking": "mask_clinical_notes"}
    elif any(term in col_lower for term in ['lab_result', 'test_value', 'measurement']):
        return {"logical_type": "numeric", "suggested_masking": "mask_lab_results"}
    elif any(term in col_lower for term in ['vital_sign', 'blood_pressure', 'heart_rate', 'temperature']):
        return {"logical_type": "numeric", "suggested_masking": "realistic_vital_signs"}

    # Dates
    elif any(term in col_lower for term in ['birth_date', 'dob', 'date_of_birth']):
        return {"logical_type": "date", "suggested_masking": "hipaa_age_group"}
    elif any(term in col_lower for term in ['appointment', 'visit_date', 'admission', 'discharge']):
        return {"logical_type": "date", "suggested_masking": "shift_days_random"}
    elif any(term in col_lower for term in ['collection_date', 'test_date', 'lab_date']):
        return {"logical_type": "date", "suggested_masking": "shift_consistent"}

    # Contact Information
    elif any(term in col_lower for term in ['phone', 'mobile', 'telephone']):
        return {"logical_type": "phone", "suggested_masking": "faker.phone_number()"}
    elif any(term in col_lower for term in ['email', 'e_mail']):
        return {"logical_type": "email", "suggested_masking": "faker.email()"}
    elif any(term in col_lower for term in ['address', 'street', 'city', 'state', 'zip']):
        return {"logical_type": "address", "suggested_masking": "faker.address()"}

    # Financial/Insurance
    elif any(term in col_lower for term in ['insurance', 'policy', 'coverage']):
        return {"logical_type": "identifier", "suggested_masking": "hash_sha256"}
    elif any(term in col_lower for term in ['ssn', 'social_security']):
        return {"logical_type": "identifier", "suggested_masking": "hash_sha256"}
    elif any(term in col_lower for term in ['account', 'billing']):
        return {"logical_type": "identifier", "suggested_masking": "tokenize"}

    # Medical Codes (usually don't need masking)
    elif any(term in col_lower for term in ['code', 'icd', 'cpt', 'procedure_code', 'diagnosis_code']):
        return {"logical_type": "code", "suggested_masking": "no masking"}
    elif any(term in col_lower for term in ['status', 'type', 'category', 'specimen_type']):
        return {"logical_type": "category", "suggested_masking": "no masking"}

    # Default based on SQL type - but check for ID patterns first
    if sql_type.lower() in ['varchar', 'text', 'char']:
        # Check if this looks like an ID even if column name doesn't indicate it
        if values and analyze_id_pattern(values):
            return {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}
        else:
            return {"logical_type": "string", "suggested_masking": "scramble"}
    elif sql_type.lower() in ['int', 'integer', 'numeric', 'decimal', 'float']:
        return {"logical_type": "numeric", "suggested_masking": "add_noise_percentage"}
    elif sql_type.lower() in ['date', 'datetime', 'timestamp']:
        return {"logical_type": "date", "suggested_masking": "shift_days_random"}

    # Safe default
    return {"logical_type": "unknown", "suggested_masking": "no masking"}

def is_complex_case(col_name, values):
    """Determine if this is a complex case that benefits from GPT analysis"""
    col_lower = col_name.lower()

    # Simple cases that rules handle well
    simple_patterns = [
        'patient_id', 'mrn', 'first_name', 'last_name', 'phone', 'email',
        'birth_date', 'ssn', 'address', 'insurance', 'code', 'status'
    ]

    if any(pattern in col_lower for pattern in simple_patterns):
        return False

    # Complex cases that benefit from GPT
    if len(values) > 0:
        # Check if values are ambiguous or complex
        sample_value = str(values[0]) if values else ""
        if len(sample_value) > 50 or any(char in sample_value for char in ['{', '[', '<', 'http']):
            return True

    return True

# --- Enhanced Masking Plan ---
def get_masking_plan(schema, df, use_gpt=True):
    cache = load_cache()
    plan = []

    for col, sql_type in schema:
        key = f"{col}|{sql_type}"

        # Check cache first
        if key in cache:
            plan.append({**cache[key], "column": col})
            continue

        values = df[col].dropna().astype(str).tolist()[:5]

        # Get healthcare rule suggestion
        rule_suggestion = get_healthcare_fallback(col, sql_type, values)

        # Use GPT for complex cases or if explicitly requested
        if use_gpt and is_complex_case(col, values):
            try:
                gpt_result = ask_gpt_for_masking(col, sql_type, values)
                # Use GPT result if it seems reasonable, otherwise fall back to rules
                if gpt_result.get("suggested_masking") != "no masking":
                    result = gpt_result
                else:
                    result = rule_suggestion
            except Exception as e:
                print(f"⚠️ GPT failed for column `{col}`, using healthcare rules: {e}")
                result = rule_suggestion
        else:
            result = rule_suggestion

        # Cache the result
        cache[key] = result
        plan.append({**result, "column": col})

    save_cache(cache)
    return plan

# --- Text/String Masking Helper Functions ---
def scramble_text(val):
    return ''.join(random.sample(str(val), len(str(val)))) if isinstance(val, str) and len(str(val)) > 1 else val

def shuffle_words(val):
    """Shuffle words in a sentence"""
    if not isinstance(val, str):
        return val
    words = str(val).split()
    if len(words) > 1:
        random.shuffle(words)
        return ' '.join(words)
    return val

def mask_partial(val):
    """Mask middle characters (Jo***hn)"""
    if not isinstance(val, str) or len(str(val)) <= 2:
        return val
    s = str(val)
    if len(s) <= 4:
        return s[0] + '*' * (len(s) - 2) + s[-1]
    else:
        return s[:2] + '*' * (len(s) - 4) + s[-2:]

def mask_first_half(val):
    """Mask first half of string"""
    if not isinstance(val, str):
        return val
    s = str(val)
    mid = len(s) // 2
    return '*' * mid + s[mid:]

def mask_last_half(val):
    """Mask last half of string"""
    if not isinstance(val, str):
        return val
    s = str(val)
    mid = len(s) // 2
    return s[:mid] + '*' * (len(s) - mid)

def deterministic_hash(val, salt="default_salt"):
    """Generate deterministic hash - same input always produces same output"""
    return hashlib.sha256((str(val) + salt).encode()).hexdigest()[:12]

def random_string_same_length(val):
    """Generate random string of same length"""
    if not isinstance(val, str):
        return val
    import string
    length = len(str(val))
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def random_string_fixed_length(val, length=8):
    """Generate random string of fixed length"""
    import string
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def character_substitution(val):
    """Replace specific characters with similar ones"""
    if not isinstance(val, str):
        return val
    substitutions = {
        'a': '@', 'e': '3', 'i': '1', 'o': '0', 's': '$',
        'A': '@', 'E': '3', 'I': '1', 'O': '0', 'S': '$'
    }
    result = str(val)
    for old, new in substitutions.items():
        result = result.replace(old, new)
    return result

def encrypt_aes(val, key=None):
    """AES encryption (requires key for decryption)"""
    if key is None:
        key = Fernet.generate_key()
    f = Fernet(key)
    encrypted = f.encrypt(str(val).encode())
    return base64.urlsafe_b64encode(encrypted).decode()

# --- Numeric Masking Helper Functions ---
def add_noise_percentage(val, percentage=10):
    """Add random noise as percentage of original value"""
    if not isinstance(val, (int, float)):
        return val
    noise_factor = percentage / 100.0
    noise = random.uniform(-noise_factor, noise_factor) * val
    return val + noise

def add_noise_fixed(val, amount=10):
    """Add fixed amount of random noise"""
    if not isinstance(val, (int, float)):
        return val
    noise = random.uniform(-amount, amount)
    return val + noise

def multiply_by_factor(val, min_factor=0.8, max_factor=1.2):
    """Multiply by random factor"""
    if not isinstance(val, (int, float)):
        return val
    factor = random.uniform(min_factor, max_factor)
    return val * factor

def round_to_nearest(val, nearest=10):
    """Round to nearest specified value"""
    if not isinstance(val, (int, float)):
        return val
    return round(val / nearest) * nearest

def binning(val, bins=None):
    """Convert numeric value to categorical bins"""
    if not isinstance(val, (int, float)):
        return val
    if bins is None:
        bins = [0, 25, 50, 75, 100]

    for i in range(len(bins) - 1):
        if bins[i] <= val < bins[i + 1]:
            return f"{bins[i]}-{bins[i + 1] - 1}"
    return f"{bins[-1]}+"

def rank_transformation(val, reference_values):
    """Replace with rank/percentile"""
    if not isinstance(val, (int, float)) or not reference_values:
        return val
    sorted_vals = sorted([v for v in reference_values if isinstance(v, (int, float))])
    if not sorted_vals:
        return val

    rank = sum(1 for v in sorted_vals if v <= val)
    percentile = (rank / len(sorted_vals)) * 100
    return f"P{int(percentile)}"

def random_in_range(val, reference_values):
    """Generate random number within min-max range of reference values"""
    if not isinstance(val, (int, float)) or not reference_values:
        return val

    numeric_vals = [v for v in reference_values if isinstance(v, (int, float))]
    if not numeric_vals:
        return val

    min_val, max_val = min(numeric_vals), max(numeric_vals)
    if isinstance(val, int):
        return random.randint(int(min_val), int(max_val))
    else:
        return random.uniform(min_val, max_val)

def categorical_binning(val):
    """Convert numeric to Low/Medium/High categories"""
    if not isinstance(val, (int, float)):
        return val

    # This is a simple implementation - in practice, you'd want to determine
    # thresholds based on the data distribution
    if val < 33:
        return "Low"
    elif val < 67:
        return "Medium"
    else:
        return "High"

# --- Date/DateTime Masking Helper Functions ---
def shift_days_random(val, min_days=-30, max_days=30):
    """Shift date by random number of days"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_days, max_days)
    return val + timedelta(days=shift)

def shift_months_random(val, min_months=-6, max_months=6):
    """Shift date by random number of months"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_months, max_months)
    # Approximate month shift (30 days per month)
    return val + timedelta(days=shift * 30)

def shift_years_random(val, min_years=-2, max_years=2):
    """Shift date by random number of years"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    shift = random.randint(min_years, max_years)
    return val + timedelta(days=shift * 365)

def shift_days_fixed(val, days=30):
    """Shift date by fixed number of days"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val + timedelta(days=days)

def year_only(val):
    """Keep only year (set to January 1st)"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

def month_year_only(val):
    """Keep only month and year (set to 1st of month)"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

def quarter_year_only(val):
    """Keep only quarter and year"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    quarter = (val.month - 1) // 3 + 1
    return f"Q{quarter} {val.year}"

def day_of_week_only(val):
    """Keep only day of week"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.strftime("%A")

def season_only(val):
    """Keep only season"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    month = val.month
    if month in [12, 1, 2]:
        return "Winter"
    elif month in [3, 4, 5]:
        return "Spring"
    elif month in [6, 7, 8]:
        return "Summer"
    else:
        return "Fall"

def age_group(val):
    """Convert birth date to age group"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    today = datetime.now()
    age = today.year - val.year - ((today.month, today.day) < (val.month, val.day))

    if age < 18:
        return "Under 18"
    elif age < 25:
        return "18-24"
    elif age < 35:
        return "25-34"
    elif age < 45:
        return "35-44"
    elif age < 55:
        return "45-54"
    elif age < 65:
        return "55-64"
    else:
        return "65+"

def remove_time_component(val):
    """Keep only date part"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.date()

def remove_date_component(val):
    """Keep only time part"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.time()

def round_to_hour(val):
    """Round to nearest hour"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(minute=0, second=0, microsecond=0)

def round_to_day(val):
    """Round to nearest day"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    return val.replace(hour=0, minute=0, second=0, microsecond=0)

def randomize_time_component(val):
    """Keep date, randomize time"""
    if not isinstance(val, (datetime, pd.Timestamp)):
        try:
            val = pd.to_datetime(val)
        except:
            return val

    random_hour = random.randint(0, 23)
    random_minute = random.randint(0, 59)
    random_second = random.randint(0, 59)

    return val.replace(hour=random_hour, minute=random_minute, second=random_second, microsecond=0)

# --- Boolean Masking Helper Functions ---
def random_boolean(val):
    """Generate random boolean"""
    return random.choice([True, False])

def flip_percentage(val, percentage=10):
    """Flip boolean value X% of the time"""
    if not isinstance(val, bool):
        return val

    if random.randint(1, 100) <= percentage:
        return not val
    return val

def maintain_boolean_distribution(val, reference_values):
    """Maintain same true/false ratio as reference data"""
    if not reference_values:
        return random.choice([True, False])

    bool_vals = [v for v in reference_values if isinstance(v, bool)]
    if not bool_vals:
        return random.choice([True, False])

    true_ratio = sum(bool_vals) / len(bool_vals)
    return random.random() < true_ratio

# --- Special/Composite Data Type Masking Functions ---
def mask_json_fields(val, fields_to_mask=None):
    """Mask specific fields in JSON data"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, str):
            data = json.loads(val)
        else:
            data = val.copy()

        if fields_to_mask is None:
            fields_to_mask = ['name', 'email', 'phone', 'address']

        def mask_recursive(obj):
            if isinstance(obj, dict):
                for key in obj:
                    if key.lower() in [f.lower() for f in fields_to_mask]:
                        obj[key] = "[MASKED]"
                    elif isinstance(obj[key], (dict, list)):
                        mask_recursive(obj[key])
            elif isinstance(obj, list):
                for item in obj:
                    if isinstance(item, (dict, list)):
                        mask_recursive(item)

        mask_recursive(data)
        return json.dumps(data) if isinstance(val, str) else data

    except (json.JSONDecodeError, TypeError):
        return val

def scramble_json_values(val):
    """Scramble all string values in JSON"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, str):
            data = json.loads(val)
        else:
            data = val.copy()

        def scramble_recursive(obj):
            if isinstance(obj, dict):
                for key in obj:
                    if isinstance(obj[key], str):
                        obj[key] = scramble_text(obj[key])
                    elif isinstance(obj[key], (dict, list)):
                        scramble_recursive(obj[key])
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    if isinstance(item, str):
                        obj[i] = scramble_text(item)
                    elif isinstance(item, (dict, list)):
                        scramble_recursive(item)

        scramble_recursive(data)
        return json.dumps(data) if isinstance(val, str) else data

    except (json.JSONDecodeError, TypeError):
        return val

def hash_json_object(val):
    """Hash entire JSON object"""
    if not isinstance(val, (str, dict, list)):
        return val

    try:
        if isinstance(val, dict):
            json_str = json.dumps(val, sort_keys=True)
        else:
            json_str = val

        return hashlib.sha256(json_str.encode()).hexdigest()

    except (TypeError, json.JSONDecodeError):
        return val

def shuffle_array(val):
    """Shuffle array elements"""
    if not isinstance(val, (list, str)):
        return val

    try:
        if isinstance(val, str):
            # Try to parse as JSON array
            data = json.loads(val)
            if isinstance(data, list):
                random.shuffle(data)
                return json.dumps(data)
            return val
        else:
            # Direct list
            shuffled = val.copy()
            random.shuffle(shuffled)
            return shuffled

    except (json.JSONDecodeError, TypeError):
        return val

def random_array_same_size(val):
    """Generate random array of same size"""
    if not isinstance(val, (list, str)):
        return val

    try:
        if isinstance(val, str):
            # Try to parse as JSON array
            data = json.loads(val)
            if isinstance(data, list):
                # Generate random array of same length
                random_data = [random.randint(1, 100) for _ in range(len(data))]
                return json.dumps(random_data)
            return val
        else:
            # Direct list
            return [random.randint(1, 100) for _ in range(len(val))]

    except (json.JSONDecodeError, TypeError):
        return val

# --- Advanced Utility Functions ---
def format_preserving_encrypt_digits(val, key=None):
    """Simple format-preserving encryption for numeric strings"""
    if not isinstance(val, str) or not val.isdigit():
        return val

    # Simple digit substitution (not cryptographically secure)
    digit_map = {
        '0': '7', '1': '4', '2': '9', '3': '1', '4': '8',
        '5': '2', '6': '0', '7': '5', '8': '3', '9': '6'
    }

    return ''.join(digit_map.get(d, d) for d in val)

def add_laplace_noise(val, sensitivity=1.0, epsilon=1.0):
    """Add Laplace noise for differential privacy"""
    if not isinstance(val, (int, float)):
        return val

    # Laplace noise: scale = sensitivity / epsilon
    scale = sensitivity / epsilon
    noise = np.random.laplace(0, scale)
    return val + noise

def add_gaussian_noise(val, sensitivity=1.0, epsilon=1.0, delta=1e-5):
    """Add Gaussian noise for differential privacy"""
    if not isinstance(val, (int, float)):
        return val

    # Gaussian noise for differential privacy
    sigma = (sensitivity * np.sqrt(2 * np.log(1.25 / delta))) / epsilon
    noise = np.random.normal(0, sigma)
    return val + noise

def generate_synthetic_similar(val, reference_values=None):
    """Generate synthetic data with similar properties"""
    if isinstance(val, str):
        # For strings, generate similar length and character types
        if not val:
            return ""

        import string
        char_types = []
        if any(c.isalpha() for c in val):
            char_types.extend(string.ascii_letters)
        if any(c.isdigit() for c in val):
            char_types.extend(string.digits)
        if any(c in string.punctuation for c in val):
            char_types.extend('.-_@')

        if not char_types:
            char_types = string.ascii_letters

        return ''.join(random.choices(char_types, k=len(val)))

    elif isinstance(val, (int, float)):
        # For numbers, generate within similar range
        if reference_values:
            numeric_refs = [v for v in reference_values if isinstance(v, (int, float))]
            if numeric_refs:
                min_val, max_val = min(numeric_refs), max(numeric_refs)
                if isinstance(val, int):
                    return random.randint(int(min_val), int(max_val))
                else:
                    return random.uniform(min_val, max_val)

        # Fallback: generate within ±50% of original value
        if isinstance(val, int):
            return random.randint(int(val * 0.5), int(val * 1.5))
        else:
            return random.uniform(val * 0.5, val * 1.5)

    return val

def preserve_data_relationships(df, columns, method="correlation"):
    """Preserve relationships between columns during masking"""
    if method == "correlation" and len(columns) >= 2:
        # Calculate correlation matrix
        numeric_cols = [col for col in columns if df[col].dtype in ['int64', 'float64']]
        if len(numeric_cols) >= 2:
            corr_matrix = df[numeric_cols].corr()
            return corr_matrix

    return None

def maintain_column_statistics(original_values, masked_values):
    """Check if statistical properties are maintained"""
    if not original_values or not masked_values:
        return {}

    orig_numeric = [v for v in original_values if isinstance(v, (int, float))]
    mask_numeric = [v for v in masked_values if isinstance(v, (int, float))]

    if not orig_numeric or not mask_numeric:
        return {}

    stats = {
        'original_mean': np.mean(orig_numeric),
        'masked_mean': np.mean(mask_numeric),
        'original_std': np.std(orig_numeric),
        'masked_std': np.std(mask_numeric),
        'mean_preserved': abs(np.mean(orig_numeric) - np.mean(mask_numeric)) < 0.1 * np.mean(orig_numeric),
        'std_preserved': abs(np.std(orig_numeric) - np.std(mask_numeric)) < 0.1 * np.std(orig_numeric)
    }

    return stats

# --- Healthcare-Specific Masking Functions ---
def generate_medical_record_number():
    """Generate realistic medical record number"""
    return f"MRN{random.randint(100000, 999999)}"

def generate_patient_id():
    """Generate realistic patient ID"""
    return f"PT{random.randint(10000, 99999)}"

def generate_insurance_number():
    """Generate realistic insurance number"""
    letters = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ', k=3))
    numbers = ''.join(random.choices('**********', k=9))
    return f"{letters}{numbers}"

def format_preserving_id_mask(val):
    """
    Preserve ID format structure while masking content.
    Analyzes pattern and maintains character types in same positions.

    Examples:
    - CLMEA1A949D → XLMEA2B847F (letters stay letters, numbers stay numbers)
    - PAT468430B7 → QBU579541C8 (preserves exact pattern)
    """
    if not isinstance(val, str) or len(val) == 0:
        return val

    result = []
    for char in val:
        if char.isalpha():
            if char.isupper():
                result.append(random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ'))
            else:
                result.append(random.choice('abcdefghijklmnopqrstuvwxyz'))
        elif char.isdigit():
            result.append(random.choice('**********'))
        else:
            # Keep special characters (hyphens, underscores, etc.)
            result.append(char)

    return ''.join(result)

def smart_id_mask(val, reference_values=None):
    """
    Smart ID masking that analyzes the pattern across multiple values
    and preserves common prefixes/suffixes while masking variable parts.
    """
    if not isinstance(val, str) or len(val) == 0:
        return val

    # If we have reference values, analyze the pattern
    if reference_values and len(reference_values) > 1:
        # Find common patterns across all values
        common_prefix = find_common_prefix([str(v) for v in reference_values if isinstance(v, str)])
        common_suffix = find_common_suffix([str(v) for v in reference_values if isinstance(v, str)])

        # Preserve common parts, mask variable parts
        if len(common_prefix) > 0 or len(common_suffix) > 0:
            prefix_len = len(common_prefix)
            suffix_len = len(common_suffix)

            if suffix_len > 0:
                middle_part = val[prefix_len:-suffix_len]
                masked_middle = format_preserving_id_mask(middle_part)
                return common_prefix + masked_middle + common_suffix
            else:
                middle_part = val[prefix_len:]
                masked_middle = format_preserving_id_mask(middle_part)
                return common_prefix + masked_middle

    # Fallback to simple format-preserving masking
    return format_preserving_id_mask(val)

def find_common_prefix(strings):
    """Find common prefix across multiple strings"""
    if not strings:
        return ""

    prefix = strings[0]
    for s in strings[1:]:
        while prefix and not s.startswith(prefix):
            prefix = prefix[:-1]
    return prefix

def find_common_suffix(strings):
    """Find common suffix across multiple strings"""
    if not strings:
        return ""

    # Reverse strings to find common prefix of reversed strings
    reversed_strings = [s[::-1] for s in strings]
    common_reversed_prefix = find_common_prefix(reversed_strings)
    return common_reversed_prefix[::-1]

def analyze_id_pattern(values):
    """
    Analyze ID pattern to determine if it's a structured ID that should use
    format-preserving masking instead of name generation.
    """
    if not values or len(values) < 2:
        return False

    string_values = [str(v) for v in values if isinstance(v, str) and len(str(v)) > 3]
    if len(string_values) < 2:
        return False

    # Check if values look like IDs (mix of letters and numbers, consistent length)
    lengths = [len(v) for v in string_values]
    avg_length = sum(lengths) / len(lengths)

    # IDs typically have consistent length
    if not (6 <= avg_length <= 20):
        return False

    # Check if most values have similar length (within 2 characters)
    length_variance = max(lengths) - min(lengths)
    if length_variance > 2:
        return False

    # Check if values contain mix of letters and numbers (typical of IDs)
    has_letters_and_numbers = 0
    for val in string_values[:5]:  # Check first 5 values
        has_letters = any(c.isalpha() for c in val)
        has_numbers = any(c.isdigit() for c in val)
        if has_letters and has_numbers:
            has_letters_and_numbers += 1

    # If most values have both letters and numbers, likely an ID
    return has_letters_and_numbers >= len(string_values[:5]) * 0.6

def mask_clinical_notes(val, sensitivity_level="high"):
    """Mask clinical notes based on sensitivity level"""
    if not isinstance(val, str):
        return val

    if sensitivity_level == "high":
        return "[CLINICAL NOTES REDACTED FOR PRIVACY]"
    elif sensitivity_level == "medium":
        # Keep structure but mask sensitive words
        sensitive_words = ['patient', 'diagnosis', 'treatment', 'medication', 'condition']
        result = str(val)
        for word in sensitive_words:
            result = result.replace(word.lower(), '[REDACTED]')
            result = result.replace(word.capitalize(), '[REDACTED]')
        return result
    else:  # low
        return scramble_text(val)

def generate_realistic_vital_signs(val, vital_type="general"):
    """Generate realistic vital signs based on type"""
    if not isinstance(val, (int, float)):
        return val

    # Realistic ranges for different vital signs
    vital_ranges = {
        'blood_pressure_systolic': (90, 180),
        'blood_pressure_diastolic': (60, 120),
        'heart_rate': (60, 100),
        'temperature': (97.0, 99.5),
        'respiratory_rate': (12, 20),
        'oxygen_saturation': (95, 100),
        'general': (int(val * 0.8), int(val * 1.2))
    }

    min_val, max_val = vital_ranges.get(vital_type, vital_ranges['general'])

    if isinstance(val, int):
        return random.randint(min_val, max_val)
    else:
        return round(random.uniform(min_val, max_val), 1)

def mask_lab_results(val, result_type="normal_range"):
    """Mask lab results while maintaining clinical relevance"""
    if not isinstance(val, (int, float)):
        return val

    if result_type == "normal_range":
        # Keep within normal ranges but add noise
        noise = random.uniform(-0.05, 0.05) * val
        return round(val + noise, 2)
    elif result_type == "categorical":
        # Convert to categorical
        if val < 33:
            return "Low"
        elif val < 67:
            return "Normal"
        else:
            return "High"
    else:
        return add_noise_percentage(val, 15)  # 15% noise for lab values

def generate_hipaa_compliant_age(birth_date):
    """Convert birth date to HIPAA-compliant age group"""
    try:
        if isinstance(birth_date, str):
            birth_date = pd.to_datetime(birth_date)

        today = datetime.now()
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))

        # HIPAA Safe Harbor age groups
        if age < 18:
            return "Under 18"
        elif age <= 24:
            return "18-24"
        elif age <= 34:
            return "25-34"
        elif age <= 44:
            return "35-44"
        elif age <= 54:
            return "45-54"
        elif age <= 64:
            return "55-64"
        elif age <= 74:
            return "65-74"
        elif age <= 84:
            return "75-84"
        else:
            return "85+"
    except:
        return "Unknown"

def preserve_medical_relationships(df, patient_id_col, date_cols):
    """Preserve relationships in medical data during masking"""
    # Create consistent shifts for each patient
    patient_shifts = {}

    for patient_id in df[patient_id_col].unique():
        if pd.notna(patient_id):
            # Same shift for all dates for this patient
            patient_shifts[patient_id] = random.randint(-30, 30)

    return patient_shifts

def mask_column(val, method, reference_values=None):
    if pd.isnull(val):
        return val

    try:
        # === FAKER METHODS ===
        if method.startswith("faker."):
            method_name = method.replace("faker.", "").replace("()", "")
            faker_fn = getattr(fake, method_name, None)
            if callable(faker_fn):
                return faker_fn()

        # === TEXT/STRING MASKING ===
        elif method == "hash_sha256":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "hash_md5":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "hash_sha1":
            return hashlib.sha1(str(val).encode()).hexdigest()
        elif method == "scramble":
            return scramble_text(val)
        elif method == "shuffle_words":
            return shuffle_words(val)
        elif method == "mask_out":
            return '*' * len(str(val))
        elif method == "mask_partial":
            return mask_partial(val)
        elif method == "mask_first_half":
            return mask_first_half(val)
        elif method == "mask_last_half":
            return mask_last_half(val)
        elif method == "redact":
            return "[REDACTED]"
        elif method == "nullify":
            return None
        elif method == "tokenize":
            return str(uuid.uuid4())[:12]
        elif method == "encrypt_aes":
            return encrypt_aes(val)
        elif method == "deterministic_hash":
            return deterministic_hash(val)
        elif method == "format_preserving_id":
            return smart_id_mask(val, reference_values)
        elif method == "random_string_same_length":
            return random_string_same_length(val)
        elif method == "random_string_fixed_length":
            return random_string_fixed_length(val)
        elif method == "substitute_from_list" and reference_values:
            return random.choice(reference_values)
        elif method == "character_substitution":
            return character_substitution(val)

        # === NUMERIC MASKING ===
        elif method == "add_noise_percentage":
            return add_noise_percentage(val)
        elif method == "add_noise_fixed":
            return add_noise_fixed(val)
        elif method == "multiply_by_factor":
            return multiply_by_factor(val)
        elif method == "round_to_nearest_10":
            return round_to_nearest(val, 10)
        elif method == "round_to_nearest_100":
            return round_to_nearest(val, 100)
        elif method == "round_to_nearest_1000":
            return round_to_nearest(val, 1000)
        elif method == "binning":
            return binning(val)
        elif method == "rank_transformation" and reference_values:
            return rank_transformation(val, reference_values)
        elif method == "random_in_range" and reference_values:
            return random_in_range(val, reference_values)
        elif method == "random_in_percentile_range" and reference_values:
            return random_in_range(val, reference_values)  # Same implementation for now
        elif method == "categorical_binning":
            return categorical_binning(val)
        elif method == "maintain_distribution" and reference_values:
            return random_in_range(val, reference_values)  # Simplified implementation
        elif method == "variance":
            if isinstance(val, (int, float)):
                noise = random.uniform(-0.1, 0.1) * val
                return val + noise

        # === DATE/DATETIME MASKING ===
        elif method == "shift_days_random":
            return shift_days_random(val)
        elif method == "shift_months_random":
            return shift_months_random(val)
        elif method == "shift_years_random":
            return shift_years_random(val)
        elif method == "shift_days_fixed":
            return shift_days_fixed(val)
        elif method == "shift_consistent":
            return shift_days_fixed(val, 30)  # Consistent 30-day shift
        elif method == "year_only":
            return year_only(val)
        elif method == "month_year_only":
            return month_year_only(val)
        elif method == "quarter_year_only":
            return quarter_year_only(val)
        elif method == "day_of_week_only":
            return day_of_week_only(val)
        elif method == "season_only":
            return season_only(val)
        elif method == "age_group":
            return age_group(val)
        elif method == "remove_time_component":
            return remove_time_component(val)
        elif method == "remove_date_component":
            return remove_date_component(val)
        elif method == "round_to_hour":
            return round_to_hour(val)
        elif method == "round_to_day":
            return round_to_day(val)
        elif method == "randomize_time_component":
            return randomize_time_component(val)

        # === BOOLEAN MASKING ===
        elif method == "random_boolean":
            return random_boolean(val)
        elif method == "flip_percentage":
            return flip_percentage(val)
        elif method == "maintain_boolean_distribution" and reference_values:
            return maintain_boolean_distribution(val, reference_values)

        # === SPECIAL/COMPOSITE DATA TYPES ===
        elif method == "mask_json_fields":
            return mask_json_fields(val)
        elif method == "scramble_json_values":
            return scramble_json_values(val)
        elif method == "hash_json_object":
            return hash_json_object(val)
        elif method == "shuffle_array":
            return shuffle_array(val)
        elif method == "random_array_same_size":
            return random_array_same_size(val)

        # === ADVANCED TECHNIQUES ===
        elif method == "format_preserving_encrypt":
            return format_preserving_encrypt_digits(val)
        elif method == "laplace_noise":
            return add_laplace_noise(val)
        elif method == "gaussian_noise":
            return add_gaussian_noise(val)
        elif method == "synthetic_similar" and reference_values:
            return generate_synthetic_similar(val, reference_values)

        # === HEALTHCARE-SPECIFIC METHODS ===
        elif method == "generate_mrn":
            return generate_medical_record_number()
        elif method == "generate_patient_id":
            return generate_patient_id()
        elif method == "generate_insurance_number":
            return generate_insurance_number()
        elif method == "mask_clinical_notes":
            return mask_clinical_notes(val)
        elif method == "realistic_vital_signs":
            return generate_realistic_vital_signs(val)
        elif method == "mask_lab_results":
            return mask_lab_results(val)
        elif method == "hipaa_age_group":
            return generate_hipaa_compliant_age(val)

        # === LEGACY/BACKWARD COMPATIBILITY ===
        elif method == "hash":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "encrypt":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "substitute" and reference_values:
            return random.choice(reference_values)
        elif "shift date" in method.lower():
            shift = random.randint(-5, 5)
            return val + timedelta(days=shift)

        # === NO MASKING ===
        elif method == "no masking":
            return val

    except Exception as e:
        print(f"⚠️ Error masking value `{val}` with method `{method}`: {e}")
        return val

    return val  # fallback

def apply_masking(df, masking_plan):
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']

        # Special handling for shuffle - operates on entire column
        if method == "shuffle":
            df[col] = df[col].sample(frac=1).reset_index(drop=True)
        else:
            # Determine if method needs reference values
            methods_needing_reference = [
                "substitute", "substitute_from_list", "rank_transformation",
                "random_in_range", "random_in_percentile_range",
                "maintain_distribution", "maintain_boolean_distribution"
            ]

            reference_values = None
            if any(ref_method in method for ref_method in methods_needing_reference):
                reference_values = df[col].dropna().tolist()

            # Apply masking to each value in the column
            df[col] = df[col].apply(lambda v: mask_column(v, method, reference_values))

    return df
