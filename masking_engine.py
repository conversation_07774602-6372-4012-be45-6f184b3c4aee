import os 
import json
import psycopg2
import pandas as pd
from dotenv import load_dotenv
from datetime import timedelta
import random
import hashlib
import uuid
from faker import Faker
from openai import OpenAI

load_dotenv()
fake = Faker()

# OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# --- Connect to PostgreSQL ---
def get_connection():
    return psycopg2.connect(
        host=os.getenv("DB_HOST"),
        port=os.getenv("DB_PORT"),
        dbname=os.getenv("DB_NAME"),
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD")
    )

# --- Get Schema + Sample Data ---
def get_schema_and_samples(table_name, limit=5):
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute(f"""
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = '{table_name}';
    """)
    schema = cursor.fetchall()

    df = pd.read_sql(f"SELECT * FROM {table_name} LIMIT {limit};", conn)

    cursor.close()
    conn.close()
    return schema, df

# --- Cache File ---
CACHE_FILE = "masking_gpt_cache.json"

def load_cache():
    return json.load(open(CACHE_FILE)) if os.path.exists(CACHE_FILE) else {}

def save_cache(cache):
    with open(CACHE_FILE, "w") as f:
        json.dump(cache, f, indent=2)

# --- GPT Call with Safe JSON Decoding ---
def ask_gpt_for_masking(col_name, sql_type, values):
    prompt = f"""
You are a data security expert.

Given the following:
- Column Name: {col_name}
- SQL Type: {sql_type}
- Sample Values: {values}

Reply ONLY with this valid JSON format (no markdown or explanation):

{{
  "logical_type": "string",
  "suggested_masking": "faker.name()"  // or hash, shift date, redact, etc.
}}
"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.2
        )
        raw = response.choices[0].message.content.strip()
        print(f"\n🧠 GPT Response for column `{col_name}`:\n{raw}\n")
        return json.loads(raw)

    except Exception as e:
        print(f"⚠️ Error parsing GPT response for column `{col_name}`: {e}")
        return {
            "logical_type": "unknown",
            "suggested_masking": "no masking"
        }

# --- Masking Plan ---
def get_masking_plan(schema, df):
    cache = load_cache()
    plan = []

    for col, sql_type in schema:
        key = f"{col}|{sql_type}"
        if key in cache:
            plan.append({**cache[key], "column": col})
            continue

        values = df[col].dropna().astype(str).tolist()[:5]
        if not values:
            continue

        result = ask_gpt_for_masking(col, sql_type, values)
        cache[key] = result
        plan.append({**result, "column": col})

    save_cache(cache)
    return plan

# --- Actual Masking Logic ---
def scramble_text(val):
    return ''.join(random.sample(str(val), len(str(val)))) if isinstance(val, str) and len(str(val)) > 1 else val

def mask_column(val, method, reference_values=None):
    if pd.isnull(val):
        return val

    try:
        if method == "faker.name()":
            return fake.name()
        elif method == "faker.email()":
            return fake.email()
        elif method == "faker.phone_number()":
            return fake.phone_number()
        elif method == "faker.address()":
            return fake.address()
        elif method.startswith("faker."):
            method_name = method.replace("faker.", "").replace("()", "")
            faker_fn = getattr(fake, method_name, None)
            if callable(faker_fn):
                return faker_fn()
        elif "shift date" in method.lower():
            shift = random.randint(-5, 5)
            return val + timedelta(days=shift)
        elif method == "hash":
            return hashlib.sha256(str(val).encode()).hexdigest()
        elif method == "redact":
            return "[REDACTED]"
        elif method == "scramble":
            return scramble_text(val)
        elif method == "mask_out":
            return '*' * len(str(val))
        elif method == "nullify":
            return None
        elif method == "encrypt":
            return hashlib.md5(str(val).encode()).hexdigest()
        elif method == "tokenize":
            return str(uuid.uuid4())[:12]
        elif method == "substitute" and reference_values:
            return random.choice(reference_values)
        elif method == "variance":
            if isinstance(val, (int, float)):
                noise = random.uniform(-0.1, 0.1) * val
                return val + noise
    except Exception as e:
        print(f"⚠️ Error masking value `{val}` with method `{method}`: {e}")
        return val

    return val  # fallback

def apply_masking(df, masking_plan):
    for col_plan in masking_plan:
        col = col_plan['column']
        method = col_plan['suggested_masking']

        if method == "shuffle":
            df[col] = df[col].sample(frac=1).reset_index(drop=True)
        else:
            reference_values = df[col].dropna().tolist() if method == "substitute" else None
            df[col] = df[col].apply(lambda v: mask_column(v, method, reference_values))

    return df
