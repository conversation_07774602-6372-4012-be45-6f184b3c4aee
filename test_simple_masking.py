#!/usr/bin/env python3
"""
Simple test to verify masking works
"""

from masking_engine import mask_column, format_preserving_id_mask

def test_basic_masking():
    """Test basic masking functions"""
    print("🧪 Testing Basic Masking Functions")
    print("=" * 40)
    
    # Test format_preserving_id_mask directly
    test_id = "100"
    masked_id = format_preserving_id_mask(test_id)
    print(f"format_preserving_id_mask: {test_id} → {masked_id}")
    
    # Test mask_column function
    masked_id2 = mask_column(test_id, "format_preserving_id", None)
    print(f"mask_column: {test_id} → {masked_id2}")
    
    # Test with actual ClaimId values
    claim_ids = ["100", "101", "888", "999"]
    print(f"\nTesting ClaimId masking:")
    for claim_id in claim_ids:
        masked = mask_column(claim_id, "format_preserving_id", claim_ids)
        print(f"  {claim_id} → {masked}")

if __name__ == "__main__":
    test_basic_masking()
