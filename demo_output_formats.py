#!/usr/bin/env python3
"""
Demo of multiple output formats for masked data
"""

import pandas as pd
from masking_engine import (
    generate_csv_output, 
    generate_json_output, 
    generate_sql_insert_statements,
    create_combined_output
)

def create_sample_data():
    """Create sample masked data like your screenshot"""
    print("📊 Creating Sample Masked Data")
    print("=" * 40)
    
    # Sample masked data (like what your tool would produce)
    claim_data = {
        'ClaimId': ['ABC123XYZ', 'DEF456UVW'],
        'ClaimDate': [None, None],
        'ClaimAmount': [4093, 3783]  # Slightly modified amounts
    }
    
    claim_detail_data = {
        'ClaimId': ['ABC123XYZ', 'ABC123XYZ', 'DEF456UVW'],
        'DiagCode': ['abc', 'cde', 'abc']
    }
    
    tables_data = {
        'Claim': pd.DataFrame(claim_data),
        'ClaimDetail': pd.DataFrame(claim_detail_data)
    }
    
    print("📋 Sample Masked Data:")
    for table_name, df in tables_data.items():
        print(f"\n{table_name}:")
        print(df.to_string(index=False))
    
    return tables_data

def demo_csv_output(tables_data):
    """Demo CSV output format"""
    print(f"\n" + "=" * 50)
    print("📄 CSV OUTPUT FORMAT")
    print("=" * 50)
    
    for table_name, df in tables_data.items():
        print(f"\n📋 {table_name}.csv:")
        print("-" * 30)
        csv_output = generate_csv_output(df, table_name)
        print(csv_output)

def demo_json_output(tables_data):
    """Demo JSON output format"""
    print(f"\n" + "=" * 50)
    print("📄 JSON OUTPUT FORMAT")
    print("=" * 50)
    
    for table_name, df in tables_data.items():
        print(f"\n📋 {table_name}.json:")
        print("-" * 30)
        json_output = generate_json_output(df, table_name)
        print(json_output)

def demo_sql_output(tables_data):
    """Demo SQL INSERT statements format"""
    print(f"\n" + "=" * 50)
    print("📄 SQL INSERT STATEMENTS FORMAT")
    print("=" * 50)
    
    for table_name, df in tables_data.items():
        print(f"\n📋 {table_name}_insert_statements.sql:")
        print("-" * 40)
        sql_output = generate_sql_insert_statements(df, table_name)
        print(sql_output)

def demo_combined_outputs(tables_data):
    """Demo combined multi-table outputs"""
    print(f"\n" + "=" * 50)
    print("📄 COMBINED MULTI-TABLE OUTPUTS")
    print("=" * 50)
    
    # Combined CSV
    print(f"\n📋 multi_table_masked_data.csv:")
    print("-" * 40)
    combined_csv = create_combined_output(tables_data, "csv")
    print(combined_csv[:500] + "..." if len(combined_csv) > 500 else combined_csv)
    
    # Combined JSON
    print(f"\n📋 multi_table_masked_data.json:")
    print("-" * 40)
    combined_json = create_combined_output(tables_data, "json")
    print(combined_json[:500] + "..." if len(combined_json) > 500 else combined_json)
    
    # Combined SQL
    print(f"\n📋 multi_table_masked_data.sql:")
    print("-" * 40)
    combined_sql = create_combined_output(tables_data, "sql")
    print(combined_sql[:500] + "..." if len(combined_sql) > 500 else combined_sql)

def show_streamlit_interface():
    """Show how the interface looks in Streamlit"""
    print(f"\n" + "=" * 50)
    print("🖥️ STREAMLIT INTERFACE")
    print("=" * 50)
    
    print(f"""
🎭 Masked Data Preview
[Data table showing masked results]

📊 Statistics:
📊 Total Rows: 2    📋 Total Columns: 3    📈 Rows Processed: 2/2

📥 Download Options
┌─────────────────────────────────────────────────────────┐
│ 📋 Select Output Format:                               │
│ [Dropdown] ▼ CSV                                       │
│           ├─ CSV                                       │
│           ├─ JSON                                      │
│           └─ SQL Insert Statements                     │
└─────────────────────────────────────────────────────────┘

[📥 Download CSV]              [📋 Generate Masking Report]

🔗 Multi-Table Mode Additional Options:
┌─────────────────────────────────────────────────────────┐
│ Select table to download individually:                 │
│ [Dropdown] ▼ Claim                                     │
│           ├─ Claim                                     │
│           └─ ClaimDetail                               │
└─────────────────────────────────────────────────────────┘

[📥 Download All Tables (CSV)]  [📥 Download Claim (CSV)]
    """)

def main():
    """Run the output formats demo"""
    print("📥 Multiple Output Formats Demo")
    print("=" * 35)
    
    # Create sample data
    tables_data = create_sample_data()
    
    # Demo each output format
    demo_csv_output(tables_data)
    demo_json_output(tables_data)
    demo_sql_output(tables_data)
    demo_combined_outputs(tables_data)
    show_streamlit_interface()
    
    print(f"\n" + "=" * 50)
    print("🎉 MULTIPLE OUTPUT FORMATS IMPLEMENTED!")
    print("=" * 50)
    
    print(f"""
✅ What You Now Have:

📄 Single Table Outputs:
• CSV format (.csv)
• JSON format (.json)  
• SQL INSERT statements (.sql)

📄 Multi-Table Outputs:
• Combined CSV with all tables
• Combined JSON with metadata
• Combined SQL with all INSERT statements
• Individual table downloads in any format

🖥️ Streamlit Interface:
• Format selection dropdown
• Dynamic file naming
• Proper MIME types for downloads
• Individual and combined download options

🎯 Your Screenshot Requirements:
✅ CSV output file
✅ JSON output file  
✅ SQL file with INSERT statements
✅ Multiple format selection
✅ Professional download interface

🚀 How to Use:
1. Run: streamlit run app.py
2. Complete your masking process
3. In Download Options section:
   - Select your preferred format (CSV/JSON/SQL)
   - Click download button
   - Get properly formatted output file

Your healthcare data masking tool now provides enterprise-grade 
output options exactly like your screenshot requirements!
    """)

if __name__ == "__main__":
    main()
