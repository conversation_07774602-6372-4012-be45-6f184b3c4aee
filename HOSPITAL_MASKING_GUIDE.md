# Hospital Management System - Data Masking Guide

## 🏥 HIPAA-Compliant Data Masking Strategies

### 👤 **Patient Demographics**

| Data Type | Column Examples | Recommended Masking | Security Level |
|-----------|----------------|-------------------|----------------|
| **Patient Names** | first_name, last_name, full_name | `faker.name()` | Medium |
| **Addresses** | street_address, city, state | `faker.address()` | Medium |
| **Phone Numbers** | home_phone, mobile_phone | `faker.phone_number()` | Medium |
| **Email Addresses** | email, contact_email | `faker.email()` | Medium |
| **SSN** | social_security_number | `hash_sha256` | High |
| **Patient ID** | patient_id, mrn | `deterministic_hash` | High |

### 🩺 **Medical Records**

| Data Type | Column Examples | Recommended Masking | Security Level |
|-----------|----------------|-------------------|----------------|
| **Medical Record Numbers** | medical_record_number | `deterministic_hash` | High |
| **Diagnosis Codes** | icd10_code, diagnosis_code | `no masking` | Low |
| **Treatment Notes** | clinical_notes, treatment_plan | `redact` | High |
| **Doctor Names** | attending_physician, doctor_name | `faker.name()` | Medium |
| **Hospital ID** | hospital_id, facility_code | `tokenize` | Medium |

### 📊 **Clinical Data**

| Data Type | Column Examples | Recommended Masking | Security Level |
|-----------|----------------|-------------------|----------------|
| **Lab Results** | glucose_level, cholesterol, hemoglobin | `add_noise_percentage` | Medium |
| **Vital Signs** | blood_pressure, heart_rate, temperature | `add_noise_percentage` | Medium |
| **Height/Weight** | height_cm, weight_kg, bmi | `add_noise_fixed` | Medium |
| **Age** | age, date_of_birth | `age_group` | Medium |
| **Lab Values (Sensitive)** | hiv_status, genetic_markers | `categorical_binning` | High |

### 📅 **Temporal Data**

| Data Type | Column Examples | Recommended Masking | Security Level |
|-----------|----------------|-------------------|----------------|
| **Birth Dates** | date_of_birth, dob | `age_group` | High |
| **Admission Dates** | admission_date, visit_date | `shift_days_random` | Medium |
| **Discharge Dates** | discharge_date, release_date | `shift_days_random` | Medium |
| **Appointment Times** | appointment_datetime | `shift_consistent` | Medium |
| **Test Dates** | lab_date, xray_date | `month_year_only` | Low |

### 💰 **Financial Information**

| Data Type | Column Examples | Recommended Masking | Security Level |
|-----------|----------------|-------------------|----------------|
| **Insurance Numbers** | insurance_id, policy_number | `hash_sha256` | High |
| **Billing Amounts** | total_charge, copay_amount | `round_to_nearest_10` | Medium |
| **Account Numbers** | account_number, billing_id | `tokenize` | High |
| **Payment Info** | credit_card_last4 | `mask_partial` | High |

### 🔬 **Research & Analytics Data**

| Data Type | Column Examples | Recommended Masking | Security Level |
|-----------|----------------|-------------------|----------------|
| **Study Participants** | participant_id | `deterministic_hash` | High |
| **Research Data** | study_results, measurements | `maintain_distribution` | Medium |
| **Cohort Information** | cohort_group, study_arm | `no masking` | Low |
| **Statistical Data** | aggregate_results | `add_noise_percentage` | Low |

## 🎯 **Masking Strategies by Use Case**

### **Development/Testing Environment**
```
✅ Use faker.* methods for realistic test data
✅ Use shift_days_random to maintain temporal relationships
✅ Use add_noise_percentage for realistic clinical values
✅ Use deterministic_hash for consistent patient IDs across tests
```

### **Analytics/Reporting**
```
✅ Use age_group instead of exact birth dates
✅ Use categorical_binning for lab results (Normal/High/Low)
✅ Use quarter_year_only for temporal analysis
✅ Use maintain_distribution for statistical validity
```

### **Research Data Sharing**
```
✅ Use hash_sha256 for complete anonymization
✅ Use synthetic_similar for realistic but fake data
✅ Use laplace_noise for differential privacy
✅ Use binning for demographic data
```

### **Audit/Compliance**
```
✅ Use redact for sensitive clinical notes
✅ Use nullify to remove unnecessary PII
✅ Use encrypt_aes for reversible masking (with proper key management)
✅ Use hash_sha256 for audit trails
```

## ⚠️ **HIPAA Compliance Notes**

### **Safe Harbor Method Compliance**
- ✅ Names → `faker.name()`
- ✅ Geographic subdivisions → `faker.city()`, `faker.state()`
- ✅ Dates → `age_group`, `shift_days_random`
- ✅ Phone numbers → `faker.phone_number()`
- ✅ Email addresses → `faker.email()`
- ✅ SSN → `hash_sha256`
- ✅ Medical record numbers → `deterministic_hash`
- ✅ Account numbers → `tokenize`

### **Statistical Disclosure Control**
- Use `add_noise_percentage` for k-anonymity
- Use `binning` for l-diversity
- Use `laplace_noise` for differential privacy
- Use `categorical_binning` for t-closeness

## 🚀 **Implementation Tips**

### **Column Detection Patterns**
Your GPT-powered suggestion engine will automatically detect:
- Patient names (first_name, last_name, patient_name)
- Medical IDs (patient_id, mrn, medical_record_number)
- Dates (birth_date, admission_date, discharge_date)
- Clinical values (lab_result, vital_sign, measurement)

### **Batch Processing**
- Process patient demographics first
- Handle temporal data with consistent shifts
- Preserve relationships between related tables
- Maintain referential integrity with deterministic hashing

### **Quality Assurance**
- Test with sample hospital data
- Verify statistical properties are preserved
- Ensure no PII leakage in masked data
- Validate temporal relationships remain logical
