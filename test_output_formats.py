#!/usr/bin/env python3
"""
Test the output format selection issue
"""

import streamlit as st
import pandas as pd
from masking_engine import generate_csv_output, generate_json_output, generate_sql_insert_statements

def test_format_selection():
    """Test format selection and download"""
    st.title("🧪 Output Format Test")
    
    # Create sample data
    sample_data = {
        'ClaimId': ['ABC123', 'DEF456'],
        'ClaimAmount': [1000, 2000],
        'PatientName': ['<PERSON>', '<PERSON>']
    }
    df = pd.DataFrame(sample_data)
    
    st.subheader("📊 Sample Data")
    st.dataframe(df)
    
    st.subheader("📥 Download Options")
    
    # Format selection
    output_format = st.selectbox(
        "📋 Select Output Format:",
        ["CSV", "JSON", "SQL Insert Statements"],
        help="Choose the format for your output"
    )
    
    st.write(f"Selected format: **{output_format}**")
    
    # Generate output based on selected format
    table_name = "TestTable"
    
    if output_format == "CSV":
        output_data = generate_csv_output(df, table_name)
        file_name = f"{table_name}_test.csv"
        mime_type = "text/csv"
    elif output_format == "JSON":
        output_data = generate_json_output(df, table_name)
        file_name = f"{table_name}_test.json"
        mime_type = "application/json"
    else:  # SQL Insert Statements
        output_data = generate_sql_insert_statements(df, table_name)
        file_name = f"{table_name}_test.sql"
        mime_type = "text/plain"
    
    # Show preview
    st.subheader(f"📄 {output_format} Preview")
    st.code(output_data[:500] + "..." if len(output_data) > 500 else output_data)
    
    # Download button
    st.download_button(
        label=f"📥 Download {output_format}",
        data=output_data.encode("utf-8"),
        file_name=file_name,
        mime=mime_type,
        help=f"Download the data as {output_format}"
    )
    
    # Debug info
    st.subheader("🔍 Debug Info")
    st.write(f"File name: {file_name}")
    st.write(f"MIME type: {mime_type}")
    st.write(f"Data length: {len(output_data)} characters")

if __name__ == "__main__":
    test_format_selection()
