{"audit_id|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "claim_id|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "action|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "performed_by|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "timestamp|timestamp without time zone": {"logical_type": "unknown", "suggested_masking": "no masking"}}