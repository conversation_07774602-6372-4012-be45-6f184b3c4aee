{"address_id|character varying": {"logical_type": "string", "suggested_masking": "hash"}, "street|character varying": {"logical_type": "string", "suggested_masking": "faker.address()"}, "city|character varying": {"logical_type": "string", "suggested_masking": "faker.address.city()"}, "state|character varying": {"logical_type": "string", "suggested_masking": "faker.address()"}, "zip_code|character varying": {"logical_type": "string", "suggested_masking": "faker.postcode()"}, "country|character varying": {"logical_type": "string", "suggested_masking": "faker.address()"}, "patient_id|character varying": {"logical_type": "string", "suggested_masking": "hash"}, "first_name|character varying": {"logical_type": "string", "suggested_masking": "faker.first_name()"}, "last_name|character varying": {"logical_type": "string", "suggested_masking": "faker.last_name()"}, "dob|date": {"logical_type": "date", "suggested_masking": "shift date"}, "gender|character varying": {"logical_type": "string", "suggested_masking": "faker.gender()"}, "email|character varying": {"logical_type": "string", "suggested_masking": "faker.email()"}, "phone_number|character varying": {"logical_type": "string", "suggested_masking": "faker.phone_number()"}, "ssn|character varying": {"logical_type": "string", "suggested_masking": "redact"}, "created_at|timestamp without time zone": {"logical_type": "timestamp", "suggested_masking": "shift date"}, "insurance_id|character varying": {"logical_type": "string", "suggested_masking": "hash"}, "risk_score_id|character varying": {"logical_type": "unknown", "suggested_masking": "no masking"}, "risk_model|character varying": {"logical_type": "string", "suggested_masking": "faker.word()"}, "risk_score|numeric": {"logical_type": "numeric", "suggested_masking": "hash"}, "risk_period_start|date": {"logical_type": "date", "suggested_masking": "shift date"}, "risk_period_end|date": {"logical_type": "date", "suggested_masking": "shift date"}, "score_source|character varying": {"logical_type": "string", "suggested_masking": "faker.company()"}, "score_date|date": {"logical_type": "unknown", "suggested_masking": "no masking"}, "diagnosis_id|character varying": {"logical_type": "string", "suggested_masking": "hash"}, "encounter_id|character varying": {"logical_type": "string", "suggested_masking": "hash"}, "diagnosis_code|character varying": {"logical_type": "unknown", "suggested_masking": "no masking"}, "diagnosis_type|character varying": {"logical_type": "string", "suggested_masking": "redact"}, "description|character varying": {"logical_type": "string", "suggested_masking": "faker.medical()"}}