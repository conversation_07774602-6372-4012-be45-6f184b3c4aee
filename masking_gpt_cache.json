{"claim_id|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "patient_id|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "provider_id|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "claim_type|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "claim_status|character varying": {"logical_type": "category", "suggested_masking": "no masking"}, "claim_submit_date|date": {"logical_type": "date", "suggested_masking": "shift_days_random"}, "claim_service_start_date|date": {"logical_type": "date", "suggested_masking": "shift_days_random"}, "claim_service_end_date|date": {"logical_type": "date", "suggested_masking": "shift_days_random"}, "total_charge_amount|numeric": {"logical_type": "numeric", "suggested_masking": "add_noise_percentage"}, "total_allowed_amount|numeric": {"logical_type": "numeric", "suggested_masking": "add_noise_percentage"}, "total_paid_amount|numeric": {"logical_type": "numeric", "suggested_masking": "add_noise_percentage"}, "patient_responsibility|numeric": {"logical_type": "numeric", "suggested_masking": "add_noise_percentage"}, "plan_id|character varying": {"logical_type": "structured_id", "suggested_masking": "format_preserving_id"}, "adjudication_date|date": {"logical_type": "date", "suggested_masking": "shift_days_random"}}