#!/usr/bin/env python3
"""
Test script to verify all the new masking methods work correctly
"""

import pandas as pd
from datetime import datetime, date
import json
from masking_engine import mask_column

def test_text_masking():
    """Test text/string masking methods"""
    print("=== Testing Text/String Masking ===")
    
    test_values = ["John Doe", "<EMAIL>", "************", "Hello World"]
    
    text_methods = [
        "faker.name()", "faker.email()", "faker.phone_number()",
        "hash_sha256", "hash_md5", "scramble", "shuffle_words",
        "mask_partial", "mask_first_half", "mask_last_half",
        "redact", "tokenize", "random_string_same_length",
        "character_substitution"
    ]
    
    for method in text_methods:
        print(f"\n{method}:")
        for val in test_values:
            try:
                result = mask_column(val, method)
                print(f"  '{val}' -> '{result}'")
            except Exception as e:
                print(f"  ERROR with '{val}': {e}")

def test_numeric_masking():
    """Test numeric masking methods"""
    print("\n=== Testing Numeric Masking ===")
    
    test_values = [100, 25.5, 1000, 0.1, -50]
    reference_values = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
    
    numeric_methods = [
        "faker.random_int()", "faker.random_number()", "faker.pyfloat()",
        "add_noise_percentage", "add_noise_fixed", "multiply_by_factor",
        "round_to_nearest_10", "round_to_nearest_100", "binning",
        "categorical_binning", "variance"
    ]
    
    for method in numeric_methods:
        print(f"\n{method}:")
        for val in test_values:
            try:
                if "random_in_range" in method or "rank_transformation" in method:
                    result = mask_column(val, method, reference_values)
                else:
                    result = mask_column(val, method)
                print(f"  {val} -> {result}")
            except Exception as e:
                print(f"  ERROR with {val}: {e}")

def test_date_masking():
    """Test date/datetime masking methods"""
    print("\n=== Testing Date/DateTime Masking ===")
    
    test_dates = [
        datetime(2023, 6, 15, 14, 30, 0),
        datetime(1990, 12, 25, 9, 0, 0),
        pd.Timestamp('2020-03-10 16:45:30')
    ]
    
    date_methods = [
        "faker.date_of_birth()", "faker.date_time()", "faker.future_date()",
        "shift_days_random", "shift_months_random", "year_only",
        "month_year_only", "quarter_year_only", "day_of_week_only",
        "season_only", "age_group", "remove_time_component",
        "round_to_hour", "randomize_time_component"
    ]
    
    for method in date_methods:
        print(f"\n{method}:")
        for val in test_dates:
            try:
                result = mask_column(val, method)
                print(f"  {val} -> {result}")
            except Exception as e:
                print(f"  ERROR with {val}: {e}")

def test_boolean_masking():
    """Test boolean masking methods"""
    print("\n=== Testing Boolean Masking ===")
    
    test_bools = [True, False, True, True, False]
    reference_bools = [True, False, True, False, True, True, False]
    
    bool_methods = ["random_boolean", "flip_percentage"]
    
    for method in bool_methods:
        print(f"\n{method}:")
        for val in test_bools:
            try:
                if "maintain_boolean_distribution" in method:
                    result = mask_column(val, method, reference_bools)
                else:
                    result = mask_column(val, method)
                print(f"  {val} -> {result}")
            except Exception as e:
                print(f"  ERROR with {val}: {e}")

def test_special_masking():
    """Test special/composite data type masking"""
    print("\n=== Testing Special/Composite Data Masking ===")
    
    test_json = '{"name": "John Doe", "email": "<EMAIL>", "age": 30}'
    test_array = '[1, 2, 3, 4, 5]'
    test_list = [10, 20, 30, 40, 50]
    
    special_methods = [
        "mask_json_fields", "scramble_json_values", "hash_json_object",
        "shuffle_array", "random_array_same_size"
    ]
    
    test_values = [test_json, test_array, test_list]
    
    for method in special_methods:
        print(f"\n{method}:")
        for val in test_values:
            try:
                result = mask_column(val, method)
                print(f"  {val} -> {result}")
            except Exception as e:
                print(f"  ERROR with {val}: {e}")

def main():
    """Run all tests"""
    print("🧪 Testing All Masking Methods")
    print("=" * 50)
    
    try:
        test_text_masking()
        test_numeric_masking()
        test_date_masking()
        test_boolean_masking()
        test_special_masking()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed! Check output above for any errors.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
