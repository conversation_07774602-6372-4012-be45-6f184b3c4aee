#!/usr/bin/env python3
"""
Debug the get_masking_plan function step by step
"""

import pandas as pd
import os
from masking_engine import get_healthcare_fallback, is_complex_case, load_cache, save_cache

def debug_get_masking_plan_step_by_step():
    """Manually step through get_masking_plan logic"""
    print("🔍 Debugging get_masking_plan Step by Step")
    print("=" * 50)
    
    # Clear cache first
    cache_file = "masking_cache.json"
    if os.path.exists(cache_file):
        os.remove(cache_file)
        print("✅ Cache cleared")
    
    # Your exact data
    your_data = {
        'claim_id': ['CLMEA1A949D', 'CLMF72F0B6C', 'CLMC90496AA'],
        'patient_id': ['PATFD81BF08', 'PAT46843087', 'PAT450BFC2B'], 
        'provider_id': ['PROVC934AC56', 'PROV131A6DFE', 'PROV644A1850']
    }
    
    df = pd.DataFrame(your_data)
    schema = [(col, 'varchar') for col in df.columns]
    use_gpt = False  # Manual mode
    
    print(f"Schema: {schema}")
    print(f"use_gpt: {use_gpt}")
    
    # Load cache
    cache = load_cache()
    print(f"Initial cache: {cache}")
    
    plan = []
    
    for col, sql_type in schema:
        print(f"\n--- Processing {col} ({sql_type}) ---")
        
        key = f"{col}|{sql_type}"
        print(f"Cache key: {key}")
        
        # Check cache first
        if key in cache:
            print(f"✅ Found in cache: {cache[key]}")
            plan.append({**cache[key], "column": col})
            continue
        else:
            print(f"❌ Not in cache")
        
        values = df[col].dropna().astype(str).tolist()[:5]
        print(f"Values: {values}")
        
        # Get healthcare rule suggestion
        print(f"Calling get_healthcare_fallback({col}, {sql_type}, {values})")
        rule_suggestion = get_healthcare_fallback(col, sql_type, values)
        print(f"Healthcare rule suggestion: {rule_suggestion}")
        
        # Use GPT for complex cases or if explicitly requested
        if use_gpt and is_complex_case(col, values):
            print(f"Would use GPT (complex case)")
            result = rule_suggestion  # Simplified for debugging
        else:
            print(f"Using healthcare rules (not complex or GPT disabled)")
            result = rule_suggestion
        
        print(f"Final result: {result}")
        
        # Cache the result
        cache[key] = result
        plan.append({**result, "column": col})
    
    print(f"\n📋 Final Plan:")
    for item in plan:
        col = item['column']
        method = item['suggested_masking']
        logical_type = item['logical_type']
        expected = "format_preserving_id"
        status = "✅" if method == expected else "❌"
        print(f"  {col}: {method} ({logical_type}) {status}")
    
    # Save cache
    save_cache(cache)
    print(f"\nCache saved: {cache}")

def test_actual_function():
    """Test the actual get_masking_plan function"""
    print("\n🔄 Testing Actual get_masking_plan Function")
    print("=" * 50)
    
    # Clear cache
    cache_file = "masking_cache.json"
    if os.path.exists(cache_file):
        os.remove(cache_file)
    
    from masking_engine import get_masking_plan
    
    your_data = {
        'claim_id': ['CLMEA1A949D', 'CLMF72F0B6C', 'CLMC90496AA'],
        'patient_id': ['PATFD81BF08', 'PAT46843087', 'PAT450BFC2B'], 
        'provider_id': ['PROVC934AC56', 'PROV131A6DFE', 'PROV644A1850']
    }
    
    df = pd.DataFrame(your_data)
    schema = [(col, 'varchar') for col in df.columns]
    
    plan = get_masking_plan(schema, df, use_gpt=False)
    
    print("📋 Actual Function Results:")
    for item in plan:
        col = item['column']
        method = item['suggested_masking']
        logical_type = item['logical_type']
        expected = "format_preserving_id"
        status = "✅" if method == expected else "❌"
        print(f"  {col}: {method} ({logical_type}) {status}")

def main():
    debug_get_masking_plan_step_by_step()
    test_actual_function()

if __name__ == "__main__":
    main()
