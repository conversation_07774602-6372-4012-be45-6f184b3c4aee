import streamlit as st
import pandas as pd
from masking_engine import (
    get_connection,
    get_schema_and_samples,
    get_masking_plan,
    apply_masking,
)

st.set_page_config(page_title="🏥 Hospital Data Masking Tool", layout="wide")
st.title("🏥 HIPAA-Compliant Hospital Data Masking Tool")
st.markdown("### 🔐 GPT-Powered Medical Data Anonymization with Manual Override")
st.markdown("---")

# Healthcare-specific info panel
with st.expander("ℹ️ HIPAA Compliance & Healthcare Data Masking Guide"):
    st.markdown("""
    **This tool is specifically designed for hospital management systems and healthcare data.**

    🔒 **HIPAA Safe Harbor Compliant:**
    - Patient names, addresses, phone numbers
    - Social Security Numbers and Medical Record Numbers
    - Birth dates and appointment dates
    - Insurance and account numbers

    🏥 **Healthcare Data Types Supported:**
    - Patient demographics and contact information
    - Medical records and clinical notes
    - Lab results and vital signs
    - Financial and insurance data
    - Appointment and treatment dates

    📊 **Preserves Medical Data Integrity:**
    - Maintains statistical relationships for research
    - Preserves temporal patterns for treatment analysis
    - Keeps clinical value distributions realistic

    🎯 **Quick Healthcare Recommendations:**
    - **Patient Names:** Use `faker.name()` for realistic replacements
    - **Medical Record Numbers:** Use `generate_mrn` or `deterministic_hash`
    - **Birth Dates:** Use `hipaa_age_group` for HIPAA compliance
    - **Clinical Notes:** Use `mask_clinical_notes` for sensitive text
    - **Lab Results:** Use `mask_lab_results` to maintain clinical relevance
    - **Vital Signs:** Use `realistic_vital_signs` for believable values
    - **Insurance Info:** Use `hash_sha256` for complete anonymization
    """)

# --- Healthcare-Focused Masking Strategy Options ---
MASKING_OPTIONS = [
    # === 🏥 HEALTHCARE-SPECIFIC OPTIONS ===
    # HIPAA Safe Harbor Compliant
    "🏥 PATIENT DATA",
    "faker.name()",                    # Patient names
    "faker.first_name()",             # First names only
    "faker.last_name()",              # Last names only
    "faker.phone_number()",           # Contact numbers
    "faker.email()",                  # Patient emails
    "faker.address()",                # Home addresses
    "faker.ssn()",                    # Social Security Numbers
    "hash_sha256",                    # Patient IDs (irreversible)
    "deterministic_hash",             # Medical Record Numbers

    "🩺 MEDICAL DATA",
    "faker.name()",                   # Doctor/Provider names
    "generate_mrn",                   # Medical Record Numbers
    "generate_patient_id",            # Patient IDs
    "generate_insurance_number",      # Insurance Numbers
    "mask_clinical_notes",            # Clinical notes (HIPAA compliant)
    "realistic_vital_signs",          # Realistic vital signs
    "mask_lab_results",               # Lab results with clinical relevance
    "mask_partial",                   # Partial masking for medical IDs
    "tokenize",                       # Hospital/Facility IDs
    "categorical_binning",            # Lab results (Normal/High/Low)
    "add_noise_percentage",           # Vital signs with realistic variance
    "binning",                        # Age groups, BMI categories

    "📅 MEDICAL DATES",
    "hipaa_age_group",                # HIPAA-compliant age groups
    "age_group",                      # Birth dates → age ranges
    "shift_days_random",              # Appointment dates (preserve patterns)
    "shift_consistent",               # Treatment timelines (same shift)
    "quarter_year_only",              # Quarterly health trends
    "year_only",                      # Long-term studies
    "month_year_only",                # Monthly health reports

    "💰 FINANCIAL/INSURANCE",
    "hash_sha256",                    # Insurance numbers (irreversible)
    "round_to_nearest_10",            # Billing amounts
    "mask_partial",                   # Credit card info
    "tokenize",                       # Account numbers

    # === 📊 RESEARCH & ANALYTICS ===
    "maintain_distribution",          # Statistical validity
    "laplace_noise",                  # Differential privacy
    "synthetic_similar",              # Realistic synthetic data

    # === 🔧 ADDITIONAL OPTIONS ===
    "📍 LOCATION DATA",
    "faker.street_address()",         # Street addresses only
    "faker.city()",                   # City names
    "faker.state()",                  # State names
    "faker.zipcode()",                # ZIP codes
    "faker.latitude()",               # GPS coordinates
    "faker.longitude()",              # GPS coordinates

    "🏢 ORGANIZATION DATA",
    "faker.company()",                # Hospital/Clinic names
    "faker.job()",                    # Job titles
    "faker.catch_phrase()",           # Organization slogans

    "🔐 SECURITY & ENCRYPTION",
    "hash_md5",                       # Fast hashing
    "hash_sha1",                      # Medium security hashing
    "encrypt_aes",                    # Reversible encryption
    "scramble",                       # Character scrambling
    "shuffle_words",                  # Word order shuffling
    "mask_out",                       # Complete masking (****)
    "mask_first_half",                # Mask first half
    "mask_last_half",                 # Mask last half
    "nullify",                        # Remove data completely
    "random_string_same_length",      # Random replacement
    "random_string_fixed_length",     # Fixed-length random
    "substitute_from_list",           # Substitute from existing values
    "character_substitution",         # Character replacement (a→@)

    "🔢 NUMERIC DATA (Lab Results, Vitals, etc.)",
    "faker.random_int()",             # Random integers
    "faker.random_number()",          # Random numbers
    "faker.pyfloat()",                # Random decimals
    "faker.credit_card_number()",     # Credit card numbers
    "add_noise_fixed",                # Fixed amount noise
    "multiply_by_factor",             # Multiply by random factor
    "round_to_nearest_100",           # Round to nearest 100
    "round_to_nearest_1000",          # Round to nearest 1000
    "rank_transformation",            # Convert to percentile ranks
    "random_in_range",                # Random within data range
    "random_in_percentile_range",     # Random within percentiles
    "variance",                       # Small random variance

    "📅 ADDITIONAL DATE OPTIONS",
    "faker.date_of_birth()",          # Random birth dates
    "faker.date_time()",              # Random date/time
    "faker.date_time_this_decade()",  # Dates within decade
    "faker.date_time_this_year()",    # Dates within year
    "faker.date_time_this_month()",   # Dates within month
    "faker.date_time_this_century()", # Dates within century
    "faker.date_time_between()",      # Dates in range
    "faker.future_date()",            # Future dates
    "faker.past_date()",              # Past dates
    "shift_months_random",            # Shift by random months
    "shift_years_random",             # Shift by random years
    "shift_days_fixed",               # Fixed day shift
    "day_of_week_only",               # Day of week only
    "season_only",                    # Season only (Spring/Summer/etc.)
    "remove_time_component",          # Date only
    "remove_date_component",          # Time only
    "round_to_hour",                  # Round to nearest hour
    "round_to_day",                   # Round to nearest day
    "randomize_time_component",       # Random time, same date

    "✅ BOOLEAN & SPECIAL DATA",
    "random_boolean",                 # Random true/false
    "flip_percentage",                # Flip 10% of values
    "maintain_boolean_distribution",  # Keep same ratio
    "mask_json_fields",               # Mask JSON fields
    "scramble_json_values",           # Scramble JSON values
    "hash_json_object",               # Hash entire JSON
    "shuffle_array",                  # Shuffle arrays
    "random_array_same_size",         # Random array same size

    "🔬 ADVANCED RESEARCH METHODS",
    "format_preserving_encrypt",      # Format-preserving encryption
    "gaussian_noise",                 # Gaussian noise (differential privacy)

    "🔄 BASIC OPTIONS",
    "hash",                           # Basic SHA-256 hash
    "shuffle",                        # Shuffle column values
    "substitute",                     # Random substitution
    "encrypt",                        # Basic MD5 encryption
    "no masking"                      # Keep original data
]


# --- Get Table List from PostgreSQL ---
@st.cache_data
def get_all_tables():
    conn = get_connection()
    cur = conn.cursor()
    cur.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    """)
    tables = [row[0] for row in cur.fetchall()]
    conn.close()
    return tables

# --- Main Logic ---
tables = get_all_tables()
selected_table = st.selectbox("📦 Select a table from PostgreSQL:", tables)

if selected_table:
    st.subheader(f"📋 Table Preview: `{selected_table}`")
    schema, df = get_schema_and_samples(selected_table, limit=5)
    st.dataframe(df)

    if st.button("🔍 Suggest Masking (via GPT + cache)"):
        with st.spinner("Generating masking suggestions..."):
            plan = get_masking_plan(schema, df)
            st.session_state["masking_plan"] = plan
            st.session_state["df"] = df

    # If masking plan exists
    if "masking_plan" in st.session_state:
        st.subheader("⚙️ Choose Masking Strategies")
        user_selected_plan = []

        for col_plan in st.session_state["masking_plan"]:
            col_name = col_plan["column"]
            gpt_suggestion = col_plan["suggested_masking"]

            selected_masking = st.selectbox(
                f"🧪 Masking for `{col_name}`",
                options=MASKING_OPTIONS,
                index=MASKING_OPTIONS.index(gpt_suggestion)
                if gpt_suggestion in MASKING_OPTIONS
                else MASKING_OPTIONS.index("no masking"),
                key=f"masking_dropdown_{col_name}",
            )
            user_selected_plan.append({
                "column": col_name,
                "suggested_masking": selected_masking
            })

        # Apply Masking Button
        if st.button("🔐 Apply Masking"):
            with st.spinner("Applying masking strategies..."):
                masked_df = apply_masking(st.session_state["df"].copy(), user_selected_plan)
                st.session_state["masked_df"] = masked_df
                st.success("✅ Masking applied successfully!")
                st.subheader("🔏 Masked Data Preview")
                st.dataframe(masked_df)

                csv = masked_df.to_csv(index=False).encode("utf-8")
                st.download_button("📥 Download Masked CSV", csv, file_name=f"{selected_table}_masked.csv")
