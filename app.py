import streamlit as st
import pandas as pd
from masking_engine import (
    get_connection,
    get_schema_and_samples,
    get_masking_plan,
    apply_masking,
    apply_selective_masking,
    generate_masking_report,
    apply_cross_table_masking,
    auto_detect_relationships,
    generate_csv_output,
    generate_json_output,
    generate_sql_insert_statements,
    create_combined_output
)

st.set_page_config(page_title=" Hospital Data Masking Tool", layout="wide")
st.title(" HIPAA-Compliant Hospital Data Masking Tool")
st.markdown("###  GPT-Powered Medical Data Anonymization with Manual Override")
st.markdown("---")

# Healthcare-specific info panel
with st.expander(" HIPAA Compliance & Healthcare Data Masking Guide"):
    st.markdown("""
    **This tool is specifically designed for hospital management systems and healthcare data.**

    🔒 **HIPAA Safe Harbor Compliant:**
    - Patient names, addresses, phone numbers
    - Social Security Numbers and Medical Record Numbers
    - Birth dates and appointment dates
    - Insurance and account numbers

    🏥 **Healthcare Data Types Supported:**
    - Patient demographics and contact information
    - Medical records and clinical notes
    - Lab results and vital signs
    - Financial and insurance data
    - Appointment and treatment dates

    📊 **Preserves Medical Data Integrity:**
    - Maintains statistical relationships for research
    - Preserves temporal patterns for treatment analysis
    - Keeps clinical value distributions realistic

    🎯 **Quick Healthcare Recommendations:**
    - **Patient Names:** Use `faker.name()` for realistic replacements
    - **Medical Record Numbers:** Use `generate_mrn` or `deterministic_hash`
    - **Birth Dates:** Use `hipaa_age_group` for HIPAA compliance
    - **Clinical Notes:** Use `mask_clinical_notes` for sensitive text
    - **Lab Results:** Use `mask_lab_results` to maintain clinical relevance
    - **Vital Signs:** Use `realistic_vital_signs` for believable values
    - **Insurance Info:** Use `hash_sha256` for complete anonymization

    🤖 **AI vs Manual Mode:**
    - **AI Mode:** GPT analyzes your data and suggests healthcare-appropriate masking
    - **Manual Mode:** Use built-in healthcare rules (faster, no API costs)
    - **Hybrid:** AI suggestions with manual override capability
    """)

# --- Healthcare-Focused Masking Strategy Options ---
MASKING_OPTIONS = [
    # === 🏥 HEALTHCARE-SPECIFIC OPTIONS ===
    # HIPAA Safe Harbor Compliant
    "🏥 PATIENT DATA",
    "faker.name()",                    # Patient names
    "faker.first_name()",             # First names only
    "faker.last_name()",              # Last names only
    "faker.phone_number()",           # Contact numbers
    "faker.email()",                  # Patient emails
    "faker.address()",                # Home addresses
    "faker.ssn()",                    # Social Security Numbers
    "hash_sha256",                    # Patient IDs (irreversible)
    "deterministic_hash",             # Medical Record Numbers

    "🩺 MEDICAL DATA",
    "faker.name()",                   # Doctor/Provider names
    "generate_mrn",                   # Medical Record Numbers
    "generate_patient_id",            # Patient IDs
    "generate_insurance_number",      # Insurance Numbers
    "mask_clinical_notes",            # Clinical notes (HIPAA compliant)
    "realistic_vital_signs",          # Realistic vital signs
    "mask_lab_results",               # Lab results with clinical relevance
    "mask_partial",                   # Partial masking for medical IDs
    "format_preserving_id",           # Smart ID masking (preserves format)
    "tokenize",                       # Hospital/Facility IDs
    "categorical_binning",            # Lab results (Normal/High/Low)
    "add_noise_percentage",           # Vital signs with realistic variance
    "binning",                        # Age groups, BMI categories

    "📅 MEDICAL DATES",
    "hipaa_age_group",                # HIPAA-compliant age groups
    "age_group",                      # Birth dates → age ranges
    "shift_days_random",              # Appointment dates (preserve patterns)
    "shift_consistent",               # Treatment timelines (same shift)
    "quarter_year_only",              # Quarterly health trends
    "year_only",                      # Long-term studies
    "month_year_only",                # Monthly health reports

    "💰 FINANCIAL/INSURANCE",
    "hash_sha256",                    # Insurance numbers (irreversible)
    "round_to_nearest_10",            # Billing amounts
    "mask_partial",                   # Credit card info
    "tokenize",                       # Account numbers

    # === 📊 RESEARCH & ANALYTICS ===
    "maintain_distribution",          # Statistical validity
    "laplace_noise",                  # Differential privacy
    "synthetic_similar",              # Realistic synthetic data

    # === 🔧 ADDITIONAL OPTIONS ===
    "📍 LOCATION DATA",
    "faker.street_address()",         # Street addresses only
    "faker.city()",                   # City names
    "faker.state()",                  # State names
    "faker.zipcode()",                # ZIP codes
    "faker.latitude()",               # GPS coordinates
    "faker.longitude()",              # GPS coordinates

    "🏢 ORGANIZATION DATA",
    "faker.company()",                # Hospital/Clinic names
    "faker.job()",                    # Job titles
    "faker.catch_phrase()",           # Organization slogans

    "🔐 SECURITY & ENCRYPTION",
    "hash_md5",                       # Fast hashing
    "hash_sha1",                      # Medium security hashing
    "encrypt_aes",                    # Reversible encryption
    "scramble",                       # Character scrambling
    "shuffle_words",                  # Word order shuffling
    "mask_out",                       # Complete masking (****)
    "mask_first_half",                # Mask first half
    "mask_last_half",                 # Mask last half
    "nullify",                        # Remove data completely
    "random_string_same_length",      # Random replacement
    "random_string_fixed_length",     # Fixed-length random
    "substitute_from_list",           # Substitute from existing values
    "character_substitution",         # Character replacement (a→@)

    "🔢 NUMERIC DATA (Lab Results, Vitals, etc.)",
    "faker.random_int()",             # Random integers
    "faker.random_number()",          # Random numbers
    "faker.pyfloat()",                # Random decimals
    "faker.credit_card_number()",     # Credit card numbers
    "add_noise_fixed",                # Fixed amount noise
    "multiply_by_factor",             # Multiply by random factor
    "round_to_nearest_100",           # Round to nearest 100
    "round_to_nearest_1000",          # Round to nearest 1000
    "rank_transformation",            # Convert to percentile ranks
    "random_in_range",                # Random within data range
    "random_in_percentile_range",     # Random within percentiles
    "variance",                       # Small random variance

    "📅 ADDITIONAL DATE OPTIONS",
    "faker.date_of_birth()",          # Random birth dates
    "faker.date_time()",              # Random date/time
    "faker.date_time_this_decade()",  # Dates within decade
    "faker.date_time_this_year()",    # Dates within year
    "faker.date_time_this_month()",   # Dates within month
    "faker.date_time_this_century()", # Dates within century
    "faker.date_time_between()",      # Dates in range
    "faker.future_date()",            # Future dates
    "faker.past_date()",              # Past dates
    "shift_months_random",            # Shift by random months
    "shift_years_random",             # Shift by random years
    "shift_days_fixed",               # Fixed day shift
    "day_of_week_only",               # Day of week only
    "season_only",                    # Season only (Spring/Summer/etc.)
    "remove_time_component",          # Date only
    "remove_date_component",          # Time only
    "round_to_hour",                  # Round to nearest hour
    "round_to_day",                   # Round to nearest day
    "randomize_time_component",       # Random time, same date

    "✅ BOOLEAN & SPECIAL DATA",
    "random_boolean",                 # Random true/false
    "flip_percentage",                # Flip 10% of values
    "maintain_boolean_distribution",  # Keep same ratio
    "mask_json_fields",               # Mask JSON fields
    "scramble_json_values",           # Scramble JSON values
    "hash_json_object",               # Hash entire JSON
    "shuffle_array",                  # Shuffle arrays
    "random_array_same_size",         # Random array same size

    "🔬 ADVANCED RESEARCH METHODS",
    "format_preserving_encrypt",      # Format-preserving encryption
    "gaussian_noise",                 # Gaussian noise (differential privacy)

    "🔄 BASIC OPTIONS",
    "hash",                           # Basic SHA-256 hash
    "shuffle",                        # Shuffle column values
    "substitute",                     # Random substitution
    "encrypt",                        # Basic MD5 encryption
    "no masking"                      # Keep original data
]


# --- Get Table List from PostgreSQL ---
@st.cache_data
def get_all_tables():
    conn = get_connection()
    cur = conn.cursor()
    cur.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    """)
    tables = [row[0] for row in cur.fetchall()]
    conn.close()
    return tables

# --- Main Logic ---
tables = get_all_tables()

# Table selection mode
masking_mode = st.radio(
    "🎯 Choose Masking Mode:",
    ["📋 Single Table Masking", "🔗 Multi-Table Masking (Preserve Relationships)"],
    help="Single table for basic masking, Multi-table to maintain relationships across tables"
)

if masking_mode == "📋 Single Table Masking":
    # Original single table logic
    selected_table = st.selectbox("📦 Select a table from PostgreSQL:", tables)

    if selected_table:
        st.subheader(f"📋 Table Preview: `{selected_table}`")
        schema, df = get_schema_and_samples(selected_table, limit=5)
        st.dataframe(df)

        # Store single table data
        st.session_state["single_table_mode"] = True
        st.session_state["selected_table"] = selected_table
        st.session_state["schema"] = schema
        st.session_state["df"] = df

else:  # Multi-table masking
    st.subheader("🔗 Multi-Table Relationship Masking")
    st.info("💡 Select multiple related tables. The tool will automatically detect relationships and ensure consistent masking across tables.")

    # Multi-select for tables
    selected_tables = st.multiselect(
        "📋 Select Related Tables to Mask",
        tables,
        help="Choose tables that share common keys (like ClaimId in both Claim and ClaimDetail tables)"
    )

    if len(selected_tables) >= 2:
        # Load data from all selected tables
        tables_data = {}
        tables_schemas = {}

        with st.spinner("Loading data from selected tables..."):
            for table in selected_tables:
                schema, df = get_schema_and_samples(table, limit=5)
                if schema and not df.empty:
                    tables_data[table] = df
                    tables_schemas[table] = schema
                else:
                    st.error(f"❌ Failed to load data from table `{table}`")
                    st.stop()

        if tables_data:
            st.success(f"✅ Loaded data from {len(tables_data)} tables")

            # Show data previews
            st.subheader("👀 Multi-Table Data Preview")
            for table_name, df in tables_data.items():
                with st.expander(f"📋 {table_name} ({len(df)} rows)"):
                    st.dataframe(df)

            # Auto-detect relationships
            relationships = auto_detect_relationships(tables_data)

            if relationships:
                st.subheader("🔗 Detected Relationships")
                st.success(f"Found {len(relationships)} potential relationships:")

                for i, rel in enumerate(relationships):
                    st.write(f"**{i+1}.** `{rel['table1']}.{rel['column1']}` ↔ `{rel['table2']}.{rel['column2']}`")

                    # Show sample values to confirm relationship
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"Sample {rel['table1']}.{rel['column1']}:")
                        sample_vals1 = tables_data[rel['table1']][rel['column1']].dropna().head(3).tolist()
                        st.code(str(sample_vals1))
                    with col2:
                        st.write(f"Sample {rel['table2']}.{rel['column2']}:")
                        sample_vals2 = tables_data[rel['table2']][rel['column2']].dropna().head(3).tolist()
                        st.code(str(sample_vals2))
            else:
                st.warning("⚠️ No relationships detected automatically. You can still proceed with independent masking.")

            # Store multi-table data
            st.session_state["single_table_mode"] = False
            st.session_state["tables_data"] = tables_data
            st.session_state["tables_schemas"] = tables_schemas
            st.session_state["relationships"] = relationships
            st.session_state["selected_tables"] = selected_tables

    elif len(selected_tables) == 1:
        st.info("💡 Select at least 2 tables for multi-table masking, or use Single Table mode.")
        st.stop()
    else:
        st.info("💡 Select tables to begin multi-table masking.")
        st.stop()

# Continue only if we have data loaded
if not (st.session_state.get("single_table_mode") or st.session_state.get("tables_data")):
    st.stop()

# AI/Manual Mode Selection
st.markdown("---")
col1, col2 = st.columns(2)

with col1:
    use_ai_mode = st.checkbox("🤖 Use AI-Powered Suggestions", value=True,
                             help="Uses GPT to analyze your data and suggest healthcare-appropriate masking methods")

with col2:
    if use_ai_mode:
        st.info("� AI will analyze column names and sample data to suggest optimal masking methods")
    else:
        st.info("⚡ Using built-in healthcare rules (faster, no API costs)")

    # # Cost and dependency warning for AI mode
    # if use_ai_mode:
    #     with st.expander("ℹ️ AI Mode Information"):
    #         st.markdown("""
    #         **AI Mode Benefits:**
    #         - 🧠 Intelligent analysis of your specific data
    #         - 🏥 Healthcare-specific suggestions
    #         - 🎯 Context-aware recommendations

    #         **Requirements:**
    #         - 🔑 OpenAI API key required
    #         - 💰 Small API costs per suggestion
    #         - 🌐 Internet connection needed

    #         **Note:** All suggestions can be manually overridden below.
    #         """)
    

    # Suggestion buttons
    col1, col2 = st.columns([3, 1])

    with col1:
        if use_ai_mode:
            suggest_button = st.button("🤖 Generate AI Suggestions (GPT + Healthcare Rules)")
        else:
            suggest_button = st.button("⚡ Apply Healthcare Rules (Fast Mode)")

    with col2:
        if st.button("🧹 Clear Cache"):
            import os
            cache_file = "masking_cache.json"
            if os.path.exists(cache_file):
                os.remove(cache_file)
                st.success("✅ Cache cleared!")
            else:
                st.info("ℹ️ No cache to clear")

    if suggest_button:
        with st.spinner("Generating masking suggestions..."):
            if st.session_state.get("single_table_mode", True):
                # Single table mode
                schema = st.session_state["schema"]
                df = st.session_state["df"]

                if use_ai_mode:
                    plan = get_masking_plan(schema, df, use_gpt=True, force_refresh=True)
                else:
                    plan = get_masking_plan(schema, df, use_gpt=False, force_refresh=True)
                st.session_state["masking_plan"] = plan
                st.session_state["df"] = df
            else:
                # Multi-table mode
                tables_data = st.session_state["tables_data"]
                tables_schemas = st.session_state["tables_schemas"]

                # Generate plans for each table
                masking_plans = {}
                for table_name, df in tables_data.items():
                    schema = tables_schemas[table_name]
                    if use_ai_mode:
                        plan = get_masking_plan(schema, df, use_gpt=True, force_refresh=True)
                    else:
                        plan = get_masking_plan(schema, df, use_gpt=False, force_refresh=True)
                    masking_plans[table_name] = plan

                st.session_state["masking_plans"] = masking_plans
                st.session_state["tables_data"] = tables_data

            # Show suggestion source
            if use_ai_mode:
                st.success("🤖 AI suggestions generated! Review and adjust as needed.")
            else:
                st.success("⚡ Healthcare rules applied! Review and adjust as needed.")

    # If masking plan exists (single table mode) or masking plans exist (multi-table mode)
    if "masking_plan" in st.session_state or "masking_plans" in st.session_state:
        st.subheader("⚙️ Customize Your Masking")

        # Determine mode and get appropriate data
        is_single_table = "masking_plan" in st.session_state and st.session_state.get("single_table_mode", True)

        if is_single_table:
            # Single table mode
            plan = st.session_state["masking_plan"]
            df = st.session_state["df"]
            total_columns = len(plan)
            total_rows = len(df)
            healthcare_suggestions = sum(1 for p in plan if p["suggested_masking"] in [
                "faker.name()", "deterministic_hash", "hipaa_age_group", "mask_clinical_notes",
                "realistic_vital_signs", "mask_lab_results", "shift_days_random", "hash_sha256", "format_preserving_id"
            ])

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("📊 Total Columns", total_columns)
            with col2:
                st.metric("📋 Total Rows", total_rows)
            with col3:
                st.metric("🏥 Healthcare Methods", healthcare_suggestions)
            with col4:
                st.metric("📈 Coverage", f"{(healthcare_suggestions/total_columns*100):.0f}%")

        else:
            # Multi-table mode
            masking_plans = st.session_state["masking_plans"]
            tables_data = st.session_state["tables_data"]
            relationships = st.session_state.get("relationships", [])

            # Calculate aggregate statistics
            total_tables = len(masking_plans)
            total_columns = sum(len(plan) for plan in masking_plans.values())
            total_rows = sum(len(df) for df in tables_data.values())
            total_relationships = len(relationships)

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("📋 Total Tables", total_tables)
            with col2:
                st.metric("📊 Total Columns", total_columns)
            with col3:
                st.metric("📋 Total Rows", total_rows)
            with col4:
                st.metric("🔗 Relationships", total_relationships)

            # Show relationship preservation info
            if relationships:
                st.info(f"🔗 {total_relationships} relationships detected - masking will preserve referential integrity across tables")
            else:
                st.warning("⚠️ No relationships detected - tables will be masked independently")

        # Row and Column Selection Options
        st.markdown("---")
        st.subheader("🎯 Select What to Mask")

        # Create tabs for different selection modes
        tab1, tab2, tab3 = st.tabs(["📋 Column Selection", "📊 Row Selection", "🎛️ Advanced Options"])

        with tab1:
            st.markdown("### 📋 Choose Columns to Mask")

            if is_single_table:
                # Single table column selection
                col_selection_mode = st.radio(
                    "Column Selection Mode:",
                    ["🔄 Mask All Columns", "✅ Select Specific Columns", "❌ Exclude Specific Columns"],
                    help="Choose how you want to select columns for masking"
                )

                selected_columns = []
                excluded_columns = []

                if col_selection_mode == "✅ Select Specific Columns":
                    st.info("💡 Select only the columns you want to mask. Unselected columns will remain unchanged.")
                    selected_columns = st.multiselect(
                        "Select columns to mask:",
                        options=[p["column"] for p in plan],
                        default=[p["column"] for p in plan],  # Default: all selected
                        help="Choose which columns should be masked"
                    )
                elif col_selection_mode == "❌ Exclude Specific Columns":
                    st.info("💡 Select columns to exclude from masking. All other columns will be masked.")
                    excluded_columns = st.multiselect(
                        "Select columns to exclude from masking:",
                        options=[p["column"] for p in plan],
                        default=[],  # Default: none excluded
                        help="Choose which columns should NOT be masked"
                    )
                else:  # Mask All Columns
                    st.info("💡 All columns will be masked according to their suggested methods.")
                    selected_columns = [p["column"] for p in plan]

            else:
                # Multi-table column selection
                st.info("💡 For multi-table masking, column selection is configured per table below.")
                st.write("**Tables and their columns:**")
                for table_name, table_plan in masking_plans.items():
                    with st.expander(f"📋 {table_name} ({len(table_plan)} columns)"):
                        cols_info = [f"• {p['column']} → {p['suggested_masking']}" for p in table_plan]
                        st.write("\n".join(cols_info))

        with tab2:
            st.markdown("### 📊 Choose Rows to Mask")

            # Row selection options
            row_selection_mode = st.radio(
                "Row Selection Mode:",
                ["🔄 Mask All Rows", "🔢 Mask Specific Number of Rows", "📍 Mask Specific Row Range", "🎲 Mask Random Sample"],
                help="Choose how you want to select rows for masking"
            )

            start_row = 0
            end_row = total_rows
            num_rows = total_rows

            if row_selection_mode == "🔢 Mask Specific Number of Rows":
                st.info("💡 Mask the first N rows of your data.")
                num_rows = st.number_input(
                    "Number of rows to mask:",
                    min_value=1,
                    max_value=total_rows,
                    value=min(100, total_rows),
                    help=f"Choose how many rows to mask (1 to {total_rows})"
                )
                end_row = num_rows

            elif row_selection_mode == "📍 Mask Specific Row Range":
                st.info("💡 Mask rows within a specific range.")
                col1, col2 = st.columns(2)
                with col1:
                    start_row = st.number_input(
                        "Start row (0-based):",
                        min_value=0,
                        max_value=total_rows-1,
                        value=0,
                        help="First row to start masking (0 = first row)"
                    )
                with col2:
                    end_row = st.number_input(
                        "End row (exclusive):",
                        min_value=start_row+1,
                        max_value=total_rows,
                        value=min(start_row+100, total_rows),
                        help="Last row to mask (exclusive)"
                    )
                num_rows = end_row - start_row

            elif row_selection_mode == "🎲 Mask Random Sample":
                st.info("💡 Mask a random sample of rows.")
                num_rows = st.number_input(
                    "Number of random rows to mask:",
                    min_value=1,
                    max_value=total_rows,
                    value=min(100, total_rows),
                    help=f"Choose how many random rows to mask (1 to {total_rows})"
                )

                # Add seed for reproducibility
                use_seed = st.checkbox("🌱 Use random seed for reproducible results")
                random_seed = None
                if use_seed:
                    random_seed = st.number_input("Random seed:", value=42, help="Same seed = same random selection")

            else:  # Mask All Rows
                st.info("💡 All rows will be masked.")
                num_rows = total_rows

            # Show row selection summary
            if row_selection_mode != "🔄 Mask All Rows":
                st.success(f"📊 Will mask {num_rows} out of {total_rows} rows ({(num_rows/total_rows*100):.1f}%)")

        with tab3:
            st.markdown("### 🎛️ Advanced Masking Options")

            # Preserve relationships option
            preserve_relationships = st.checkbox(
                "🔗 Preserve data relationships",
                value=True,
                help="Maintain consistent masking across related records (e.g., same patient ID gets same masked value)"
            )

            # Output format options
            st.markdown("#### 📁 Output Options")
            include_original = st.checkbox(
                "📋 Include original data for comparison",
                value=False,
                help="Add original columns alongside masked columns for comparison"
            )

            add_mask_indicators = st.checkbox(
                "🏷️ Add masking indicators",
                value=False,
                help="Add columns showing which masking method was used for each field"
            )

            # Preview options
            st.markdown("#### 👀 Preview Options")
            preview_rows = st.number_input(
                "Number of rows to preview:",
                min_value=1,
                max_value=20,
                value=5,
                help="How many rows to show in the preview"
            )

        # Masking Strategy Configuration
        st.markdown("---")
        st.subheader("🧪 Configure Masking Methods")

        if is_single_table:
            # Single table masking configuration
            st.info("💡 Customize the masking method for each column. Only selected columns will be processed.")

            user_selected_plan = []

            # Determine which columns to show based on selection
            if col_selection_mode == "✅ Select Specific Columns":
                columns_to_configure = [p for p in plan if p["column"] in selected_columns]
            elif col_selection_mode == "❌ Exclude Specific Columns":
                columns_to_configure = [p for p in plan if p["column"] not in excluded_columns]
            else:  # Mask All Columns
                columns_to_configure = plan

            for col_plan in columns_to_configure:
                col_name = col_plan["column"]
                gpt_suggestion = col_plan["suggested_masking"]
                logical_type = col_plan.get("logical_type", "unknown")

                # Add helpful context
                help_text = f"Data type: {logical_type} | Suggested: {gpt_suggestion}"

                # Show if column is selected for masking
                mask_status = "🟢 WILL BE MASKED" if col_name in [p["column"] for p in columns_to_configure] else "🔴 WILL NOT BE MASKED"

                selected_masking = st.selectbox(
                    f"🧪 Masking for `{col_name}` ({mask_status})",
                    options=MASKING_OPTIONS,
                    index=MASKING_OPTIONS.index(gpt_suggestion)
                    if gpt_suggestion in MASKING_OPTIONS
                    else MASKING_OPTIONS.index("no masking"),
                    key=f"masking_dropdown_{col_name}",
                    help=help_text
                )
                user_selected_plan.append({
                    "column": col_name,
                    "suggested_masking": selected_masking
                })

            # Add unselected columns with "no masking"
            for col_plan in plan:
                if col_plan["column"] not in [p["column"] for p in columns_to_configure]:
                    user_selected_plan.append({
                        "column": col_plan["column"],
                        "suggested_masking": "no masking"
                    })

        else:
            # Multi-table masking configuration
            st.info("💡 Configure masking methods for each table. Relationships will be preserved automatically.")

            # Store the updated plans
            updated_masking_plans = {}

            for table_name, table_plan in masking_plans.items():
                st.markdown(f"### 📋 {table_name}")

                updated_plan = []
                for col_plan in table_plan:
                    col_name = col_plan["column"]
                    gpt_suggestion = col_plan["suggested_masking"]
                    logical_type = col_plan.get("logical_type", "unknown")

                    # Add helpful context
                    help_text = f"Data type: {logical_type} | Suggested: {gpt_suggestion}"

                    selected_masking = st.selectbox(
                        f"🧪 Masking for `{table_name}.{col_name}`",
                        options=MASKING_OPTIONS,
                        index=MASKING_OPTIONS.index(gpt_suggestion)
                        if gpt_suggestion in MASKING_OPTIONS
                        else MASKING_OPTIONS.index("no masking"),
                        key=f"masking_dropdown_{table_name}_{col_name}",
                        help=help_text
                    )
                    updated_plan.append({
                        "column": col_name,
                        "suggested_masking": selected_masking
                    })

                updated_masking_plans[table_name] = updated_plan

            # Update the session state with modified plans
            st.session_state["masking_plans"] = updated_masking_plans

        # Store selection parameters in session state
        st.session_state["row_selection_mode"] = row_selection_mode
        st.session_state["start_row"] = start_row
        st.session_state["end_row"] = end_row
        st.session_state["num_rows"] = num_rows
        st.session_state["random_seed"] = random_seed if row_selection_mode == "🎲 Mask Random Sample" else None
        st.session_state["preserve_relationships"] = preserve_relationships
        st.session_state["include_original"] = include_original
        st.session_state["add_mask_indicators"] = add_mask_indicators
        st.session_state["preview_rows"] = preview_rows

        # Apply Masking Button
        st.markdown("---")
        if st.button("🎭 Apply Customized Masking", type="primary"):
            with st.spinner("Applying customized masking..."):
                # Get selection parameters
                row_selection_mode = st.session_state.get("row_selection_mode", "🔄 Mask All Rows")
                start_row = st.session_state.get("start_row", 0)
                random_seed = st.session_state.get("random_seed", None)
                preserve_relationships = st.session_state.get("preserve_relationships", True)
                include_original = st.session_state.get("include_original", False)
                add_mask_indicators = st.session_state.get("add_mask_indicators", False)
                preview_rows = st.session_state.get("preview_rows", 5)

                if is_single_table:
                    # Single table masking
                    end_row = st.session_state.get("end_row", len(st.session_state["df"]))
                    num_rows = st.session_state.get("num_rows", len(st.session_state["df"]))

                    # Apply selective masking
                    masked_df = apply_selective_masking(
                        st.session_state["df"],
                        user_selected_plan,
                        row_selection_mode=row_selection_mode,
                        start_row=start_row,
                        end_row=end_row,
                        num_rows=num_rows,
                        random_seed=random_seed,
                        preserve_relationships=preserve_relationships,
                        include_original=include_original,
                        add_mask_indicators=add_mask_indicators
                    )
                    st.session_state["masked_df"] = masked_df

                    # Show masking summary
                    original_rows = len(st.session_state["df"])
                    masked_rows = len(masked_df)
                    masked_columns = len([p for p in user_selected_plan if p["suggested_masking"] != "no masking"])
                    total_columns = len(user_selected_plan)

                    st.success(f"✅ Single table masking completed! {masked_rows} rows × {masked_columns}/{total_columns} columns processed")

                else:
                    # Multi-table masking with relationship preservation
                    tables_data = st.session_state["tables_data"]
                    masking_plans = st.session_state["masking_plans"]
                    relationships = st.session_state.get("relationships", [])

                    # Apply cross-table masking
                    masked_tables, global_mappings = apply_cross_table_masking(
                        tables_data,
                        masking_plans,
                        relationships,
                        preserve_relationships=preserve_relationships
                    )

                    st.session_state["masked_tables"] = masked_tables
                    st.session_state["global_mappings"] = global_mappings

                    # Show masking summary
                    total_tables = len(masked_tables)
                    total_original_rows = sum(len(df) for df in tables_data.values())
                    total_masked_rows = sum(len(df) for df in masked_tables.values())
                    total_relationships_preserved = len(global_mappings)

                    st.success(f"✅ Multi-table masking completed! {total_tables} tables, {total_masked_rows} total rows, {total_relationships_preserved} relationships preserved")

                # Display results
                st.subheader("🎭 Masked Data Preview")

                if is_single_table:
                    # Single table results
                    preview_df = masked_df.head(preview_rows)
                    st.dataframe(preview_df)

                    # Show masking statistics
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("📊 Total Rows", len(masked_df))
                    with col2:
                        st.metric("📋 Total Columns", len(masked_df.columns))
                    with col3:
                        st.metric("📈 Rows Processed", f"{masked_rows}/{original_rows}")

                    # Download options
                    st.markdown("---")
                    st.subheader("📥 Download Options")

                    # Output format selection with explicit state management
                    st.write("### 📋 Select Output Format:")

                    # Create three columns for format selection buttons
                    col_csv, col_json, col_sql = st.columns(3)

                    # Initialize format in session state if not exists
                    if "selected_output_format" not in st.session_state:
                        st.session_state.selected_output_format = "CSV"

                    with col_csv:
                        if st.button("📊 CSV Format", use_container_width=True,
                                   type="primary" if st.session_state.selected_output_format == "CSV" else "secondary"):
                            st.session_state.selected_output_format = "CSV"

                    with col_json:
                        if st.button("📋 JSON Format", use_container_width=True,
                                   type="primary" if st.session_state.selected_output_format == "JSON" else "secondary"):
                            st.session_state.selected_output_format = "JSON"

                    with col_sql:
                        if st.button("🗄️ SQL Format", use_container_width=True,
                                   type="primary" if st.session_state.selected_output_format == "SQL" else "secondary"):
                            st.session_state.selected_output_format = "SQL"

                    # Get current selection
                    output_format = st.session_state.selected_output_format
                    table_name = st.session_state.get('selected_table', 'table')

                    # Debug info
                    st.success(f"🔍 **Selected format: {output_format}** | File will be: `{table_name}_masked.{output_format.lower()}`")

                    # Generate output based on selected format
                    if output_format == "CSV":
                        output_data = generate_csv_output(masked_df, table_name)
                        file_name = f"{table_name}_masked.csv"
                        mime_type = "text/csv"
                        format_icon = "📊"
                    elif output_format == "JSON":
                        output_data = generate_json_output(masked_df, table_name)
                        file_name = f"{table_name}_masked.json"
                        mime_type = "application/json"
                        format_icon = "📋"
                    else:  # SQL
                        output_data = generate_sql_insert_statements(masked_df, table_name)
                        file_name = f"{table_name}_insert_statements.sql"
                        mime_type = "text/plain"
                        format_icon = "🗄️"

                    # Show preview of output
                    st.write(f"### {format_icon} {output_format} Preview:")
                    st.code(output_data[:300] + "..." if len(output_data) > 300 else output_data, language=output_format.lower())

                    col1, col2 = st.columns(2)

                    with col1:
                        # Download masked data in selected format
                        st.download_button(
                            label=f"{format_icon} Download {output_format}",
                            data=output_data.encode("utf-8"),
                            file_name=file_name,
                            mime=mime_type,
                            help=f"Download the masked dataset as {output_format}",
                            use_container_width=True
                        )

                    with col2:
                        # Download masking report
                        if st.button("📋 Generate Masking Report"):
                            report = generate_masking_report(
                                st.session_state["df"],
                                masked_df,
                                user_selected_plan,
                                st.session_state
                            )
                            st.download_button(
                                label="📄 Download Masking Report",
                                data=report,
                                file_name=f"{table_name}_masking_report.txt",
                                mime="text/plain",
                                help="Download a detailed report of the masking process"
                            )

                else:
                    # Multi-table results
                    masked_tables = st.session_state["masked_tables"]

                    # Show preview for each table
                    for table_name, masked_df in masked_tables.items():
                        with st.expander(f"📋 {table_name} Preview ({len(masked_df)} rows)"):
                            preview_df = masked_df.head(preview_rows)
                            st.dataframe(preview_df)

                    # Show relationship preservation info
                    if st.session_state.get("global_mappings"):
                        st.subheader("🔗 Relationship Preservation")
                        st.success("✅ Referential integrity maintained across all tables")

                        # Show some examples of consistent masking
                        global_mappings = st.session_state["global_mappings"]
                        if global_mappings:
                            st.write("**Examples of consistent masking:**")
                            for rel_key, mapping_info in list(global_mappings.items())[:3]:  # Show first 3
                                table1 = mapping_info['table1']
                                column1 = mapping_info['column1']
                                table2 = mapping_info['table2']
                                column2 = mapping_info['column2']

                                # Show a sample mapping
                                sample_mappings = list(mapping_info['mapping'].items())[:2]  # First 2 mappings
                                for original, masked in sample_mappings:
                                    st.write(f"• `{table1}.{column1}` & `{table2}.{column2}`: {original} → {masked}")

                    # Download options for multi-table
                    st.markdown("---")
                    st.subheader("📥 Download Options")

                    # Output format selection for multi-table with buttons
                    st.write("### 📋 Select Output Format:")

                    # Create three columns for format selection buttons
                    col_csv, col_json, col_sql = st.columns(3)

                    # Initialize multi-table format in session state if not exists
                    if "selected_multi_output_format" not in st.session_state:
                        st.session_state.selected_multi_output_format = "CSV"

                    with col_csv:
                        if st.button("📊 CSV Format", use_container_width=True,
                                   type="primary" if st.session_state.selected_multi_output_format == "CSV" else "secondary",
                                   key="multi_csv_btn"):
                            st.session_state.selected_multi_output_format = "CSV"

                    with col_json:
                        if st.button("📋 JSON Format", use_container_width=True,
                                   type="primary" if st.session_state.selected_multi_output_format == "JSON" else "secondary",
                                   key="multi_json_btn"):
                            st.session_state.selected_multi_output_format = "JSON"

                    with col_sql:
                        if st.button("🗄️ SQL Format", use_container_width=True,
                                   type="primary" if st.session_state.selected_multi_output_format == "SQL" else "secondary",
                                   key="multi_sql_btn"):
                            st.session_state.selected_multi_output_format = "SQL"

                    # Get current selection
                    output_format = st.session_state.selected_multi_output_format

                    # Debug info
                    st.success(f"🔍 **Multi-table format: {output_format}** | Combined file: `multi_table_masked_data.{output_format.lower()}`")

                    # Generate combined output based on selected format
                    format_map = {"CSV": "csv", "JSON": "json", "SQL": "sql"}
                    combined_output = create_combined_output(masked_tables, format_map[output_format])

                    # File extension and MIME type mapping
                    file_extensions = {"CSV": ".csv", "JSON": ".json", "SQL": ".sql"}
                    mime_types = {"CSV": "text/csv", "JSON": "application/json", "SQL": "text/plain"}
                    format_icons = {"CSV": "📊", "JSON": "📋", "SQL": "🗄️"}

                    # Show preview of combined output
                    st.write(f"### {format_icons[output_format]} Combined {output_format} Preview:")
                    st.code(combined_output[:400] + "..." if len(combined_output) > 400 else combined_output, language=output_format.lower())

                    col1, col2 = st.columns(2)

                    with col1:
                        st.download_button(
                            label=f"{format_icons[output_format]} Download All Tables ({output_format})",
                            data=combined_output.encode("utf-8"),
                            file_name=f"multi_table_masked_data{file_extensions[output_format]}",
                            mime=mime_types[output_format],
                            help=f"Download all masked tables in one {output_format} file",
                            use_container_width=True
                        )

                    with col2:
                        # Individual table downloads
                        selected_table_download = st.selectbox(
                            "Select table to download individually:",
                            options=list(masked_tables.keys()),
                            key="individual_table_select"
                        )

                        if selected_table_download:
                            # Generate individual table output
                            if output_format == "CSV":
                                individual_data = generate_csv_output(masked_tables[selected_table_download], selected_table_download)
                            elif output_format == "JSON":
                                individual_data = generate_json_output(masked_tables[selected_table_download], selected_table_download)
                            else:  # SQL
                                individual_data = generate_sql_insert_statements(masked_tables[selected_table_download], selected_table_download)

                            st.download_button(
                                label=f"{format_icons[output_format]} Download {selected_table_download} ({output_format})",
                                data=individual_data.encode("utf-8"),
                                file_name=f"{selected_table_download}_masked{file_extensions[output_format]}",
                                mime=mime_types[output_format],
                                help=f"Download only the {selected_table_download} table as {output_format}",
                                use_container_width=True
                            )
