import streamlit as st
import pandas as pd
from masking_engine import (
    get_connection,
    get_schema_and_samples,
    get_masking_plan,
    apply_masking,
)

st.set_page_config(page_title="🔐 Data Masking Tool", layout="wide")
st.title("🔐 GPT-Powered Data Masking with Manual Override")

# --- Masking strategy options ---
MASKING_OPTIONS = [
    "faker.name()",
    "faker.email()",
    "faker.phone_number()",
    "faker.address()",
    "faker.date_of_birth()",
    "faker.date_time()",
    "faker.date_time_this_decade()",
    "faker.date_time_this_month()",
    "faker.date_time_this_year()",
    "faker.date_time_between_dates()",
    "faker.date_time_between()",
    "faker.date_time_this_century()",
    "faker.date_time_this_month()",
    "faker.date_time_this_year()",
    "shift date by random -5 to +5 days",
    "hash",
    "scramble",
    "shuffle",
    "substitute",
    "variance",
    "mask_out",
    "nullify",
    "encrypt",
    "tokenize",
    "redact",
    "no masking"
]


# --- Get Table List from PostgreSQL ---
@st.cache_data
def get_all_tables():
    conn = get_connection()
    cur = conn.cursor()
    cur.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    """)
    tables = [row[0] for row in cur.fetchall()]
    conn.close()
    return tables

# --- Main Logic ---
tables = get_all_tables()
selected_table = st.selectbox("📦 Select a table from PostgreSQL:", tables)

if selected_table:
    st.subheader(f"📋 Table Preview: `{selected_table}`")
    schema, df = get_schema_and_samples(selected_table, limit=5)
    st.dataframe(df)

    if st.button("🔍 Suggest Masking (via GPT + cache)"):
        with st.spinner("Generating masking suggestions..."):
            plan = get_masking_plan(schema, df)
            st.session_state["masking_plan"] = plan
            st.session_state["df"] = df

    # If masking plan exists
    if "masking_plan" in st.session_state:
        st.subheader("⚙️ Choose Masking Strategies")
        user_selected_plan = []

        for col_plan in st.session_state["masking_plan"]:
            col_name = col_plan["column"]
            gpt_suggestion = col_plan["suggested_masking"]

            selected_masking = st.selectbox(
                f"🧪 Masking for `{col_name}`",
                options=MASKING_OPTIONS,
                index=MASKING_OPTIONS.index(gpt_suggestion)
                if gpt_suggestion in MASKING_OPTIONS
                else MASKING_OPTIONS.index("no masking"),
                key=f"masking_dropdown_{col_name}",
            )
            user_selected_plan.append({
                "column": col_name,
                "suggested_masking": selected_masking
            })

        # Apply Masking Button
        if st.button("🔐 Apply Masking"):
            with st.spinner("Applying masking strategies..."):
                masked_df = apply_masking(st.session_state["df"].copy(), user_selected_plan)
                st.session_state["masked_df"] = masked_df
                st.success("✅ Masking applied successfully!")
                st.subheader("🔏 Masked Data Preview")
                st.dataframe(masked_df)

                csv = masked_df.to_csv(index=False).encode("utf-8")
                st.download_button("📥 Download Masked CSV", csv, file_name=f"{selected_table}_masked.csv")
