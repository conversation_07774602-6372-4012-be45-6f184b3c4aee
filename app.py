import streamlit as st
import pandas as pd
from masking_engine import (
    get_connection,
    get_schema_and_samples,
    get_masking_plan,
    apply_masking,
)

st.set_page_config(page_title="🔐 Data Masking Tool", layout="wide")
st.title("🔐 GPT-Powered Data Masking with Manual Override")

# --- Masking strategy options ---
MASKING_OPTIONS = [
    # === TEXT/STRING DATA TYPES ===
    # Personal Identifiers
    "faker.name()",
    "faker.first_name()",
    "faker.last_name()",
    "faker.email()",
    "faker.phone_number()",
    "faker.ssn()",
    "faker.license_plate()",
    "faker.passport_number()",

    # Address & Location
    "faker.address()",
    "faker.street_address()",
    "faker.city()",
    "faker.state()",
    "faker.country()",
    "faker.zipcode()",
    "faker.latitude()",
    "faker.longitude()",

    # Business & Professional
    "faker.company()",
    "faker.job()",
    "faker.catch_phrase()",
    "faker.bs()",

    # Text Transformation Methods
    "hash_sha256",
    "hash_md5",
    "hash_sha1",
    "scramble",
    "shuffle_words",
    "mask_out",
    "mask_partial",
    "mask_first_half",
    "mask_last_half",
    "redact",
    "nullify",
    "tokenize",
    "encrypt_aes",
    "deterministic_hash",
    "random_string_same_length",
    "random_string_fixed_length",
    "substitute_from_list",
    "character_substitution",

    # === NUMERIC DATA TYPES ===
    # Faker Numeric Data
    "faker.random_int()",
    "faker.random_number()",
    "faker.pyfloat()",
    "faker.credit_card_number()",

    # Mathematical Transformations
    "add_noise_percentage",
    "add_noise_fixed",
    "multiply_by_factor",
    "round_to_nearest_10",
    "round_to_nearest_100",
    "round_to_nearest_1000",
    "binning",
    "rank_transformation",

    # Range-Based Methods
    "random_in_range",
    "random_in_percentile_range",
    "categorical_binning",

    # Preservation Methods
    "maintain_distribution",
    "variance",

    # === DATE/DATETIME DATA TYPES ===
    # Faker Date Methods
    "faker.date_of_birth()",
    "faker.date_time()",
    "faker.date_time_this_decade()",
    "faker.date_time_this_year()",
    "faker.date_time_this_month()",
    "faker.date_time_this_century()",
    "faker.date_time_between()",
    "faker.future_date()",
    "faker.past_date()",

    # Date Shifting Methods
    "shift_days_random",
    "shift_months_random",
    "shift_years_random",
    "shift_days_fixed",
    "shift_consistent",

    # Date Masking Methods
    "year_only",
    "month_year_only",
    "quarter_year_only",
    "day_of_week_only",
    "season_only",
    "age_group",

    # Time Component Methods
    "remove_time_component",
    "remove_date_component",
    "round_to_hour",
    "round_to_day",
    "randomize_time_component",

    # === BOOLEAN DATA TYPES ===
    "random_boolean",
    "flip_percentage",
    "maintain_boolean_distribution",

    # === SPECIAL/COMPOSITE DATA TYPES ===
    "mask_json_fields",
    "scramble_json_values",
    "hash_json_object",
    "shuffle_array",
    "random_array_same_size",

    # === ADVANCED TECHNIQUES ===
    "format_preserving_encrypt",
    "laplace_noise",
    "gaussian_noise",
    "synthetic_similar",

    # === LEGACY/EXISTING OPTIONS ===
    "hash",  # kept for backward compatibility
    "shuffle",
    "substitute",
    "encrypt",  # kept for backward compatibility
    "no masking"
]


# --- Get Table List from PostgreSQL ---
@st.cache_data
def get_all_tables():
    conn = get_connection()
    cur = conn.cursor()
    cur.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    """)
    tables = [row[0] for row in cur.fetchall()]
    conn.close()
    return tables

# --- Main Logic ---
tables = get_all_tables()
selected_table = st.selectbox("📦 Select a table from PostgreSQL:", tables)

if selected_table:
    st.subheader(f"📋 Table Preview: `{selected_table}`")
    schema, df = get_schema_and_samples(selected_table, limit=5)
    st.dataframe(df)

    if st.button("🔍 Suggest Masking (via GPT + cache)"):
        with st.spinner("Generating masking suggestions..."):
            plan = get_masking_plan(schema, df)
            st.session_state["masking_plan"] = plan
            st.session_state["df"] = df

    # If masking plan exists
    if "masking_plan" in st.session_state:
        st.subheader("⚙️ Choose Masking Strategies")
        user_selected_plan = []

        for col_plan in st.session_state["masking_plan"]:
            col_name = col_plan["column"]
            gpt_suggestion = col_plan["suggested_masking"]

            selected_masking = st.selectbox(
                f"🧪 Masking for `{col_name}`",
                options=MASKING_OPTIONS,
                index=MASKING_OPTIONS.index(gpt_suggestion)
                if gpt_suggestion in MASKING_OPTIONS
                else MASKING_OPTIONS.index("no masking"),
                key=f"masking_dropdown_{col_name}",
            )
            user_selected_plan.append({
                "column": col_name,
                "suggested_masking": selected_masking
            })

        # Apply Masking Button
        if st.button("🔐 Apply Masking"):
            with st.spinner("Applying masking strategies..."):
                masked_df = apply_masking(st.session_state["df"].copy(), user_selected_plan)
                st.session_state["masked_df"] = masked_df
                st.success("✅ Masking applied successfully!")
                st.subheader("🔏 Masked Data Preview")
                st.dataframe(masked_df)

                csv = masked_df.to_csv(index=False).encode("utf-8")
                st.download_button("📥 Download Masked CSV", csv, file_name=f"{selected_table}_masked.csv")
