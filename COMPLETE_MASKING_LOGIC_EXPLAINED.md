# 🧠 Complete Healthcare Data Masking Logic - All Features Explained

## 📋 **OVERVIEW - What Your Tool Does**

Your healthcare data masking tool provides **enterprise-grade data anonymization** with:
- **Single & Multi-table masking** with relationship preservation
- **AI-powered & rule-based suggestions** for optimal masking methods
- **Selective row/column masking** for custom requirements
- **Format-preserving ID masking** for structured identifiers
- **HIPAA-compliant anonymization** for healthcare data

---

## 🏗️ **ARCHITECTURE - How It's Built**

### **📁 File Structure:**
```
📁 Your Project/
├── 🎯 app.py                    # Main Streamlit interface
├── 🎯 masking_engine.py         # Core masking logic
├── 📋 Documentation files       # Feature explanations
└── 🧪 Test files               # Verification scripts
```

### **🔄 Data Flow:**
```
Database → Table Selection → AI/Manual Suggestions → 
User Configuration → Masking Application → Results & Download
```

---

## 🎛️ **USER INTERFACE LOGIC (app.py)**

### **1. 🎯 Mode Selection Logic:**
```python
masking_mode = st.radio(
    "🎯 Choose Masking Mode:",
    ["📋 Single Table Masking", "🔗 Multi-Table Masking"]
)
```
**Logic:** User chooses between single table or multi-table with relationship preservation.

### **2. 📊 Table Selection Logic:**

#### **Single Table Mode:**
```python
if masking_mode == "📋 Single Table Masking":
    selected_table = st.selectbox("📦 Select a table:", tables)
    schema, df = get_schema_and_samples(selected_table)
    st.session_state["single_table_mode"] = True
```

#### **Multi-Table Mode:**
```python
else:  # Multi-table masking
    selected_tables = st.multiselect("📋 Select Related Tables:", tables)
    for table in selected_tables:
        tables_data[table] = get_schema_and_samples(table)
    relationships = auto_detect_relationships(tables_data)
    st.session_state["single_table_mode"] = False
```

**Logic:** Single mode loads one table, multi-mode loads multiple tables and detects relationships.

### **3. 🤖 AI/Manual Mode Logic:**
```python
use_ai_mode = st.checkbox("🤖 Use AI-Powered Suggestions", value=True)

if suggest_button:
    if use_ai_mode:
        plan = get_masking_plan(schema, df, use_gpt=True)
    else:
        plan = get_masking_plan(schema, df, use_gpt=False)
```

**Logic:** User chooses between GPT-powered suggestions or built-in healthcare rules.

### **4. ⚙️ Customization Logic:**

#### **Column Selection:**
```python
col_selection_mode = st.radio([
    "🔄 Mask All Columns",
    "✅ Select Specific Columns", 
    "❌ Exclude Specific Columns"
])

if col_selection_mode == "✅ Select Specific Columns":
    selected_columns = st.multiselect("Select columns:", all_columns)
```

#### **Row Selection:**
```python
row_selection_mode = st.radio([
    "🔄 Mask All Rows",
    "🔢 Mask Specific Number of Rows",
    "📍 Mask Specific Row Range",
    "🎲 Mask Random Sample"
])
```

**Logic:** Users can select exactly which columns and rows to mask.

### **5. 🎭 Masking Application Logic:**
```python
if st.button("🎭 Apply Customized Masking"):
    if is_single_table:
        masked_df = apply_selective_masking(df, user_plan, **options)
    else:
        masked_tables = apply_cross_table_masking(tables_data, plans, relationships)
```

**Logic:** Applies different masking functions based on single vs multi-table mode.

---

## 🧠 **CORE MASKING ENGINE LOGIC (masking_engine.py)**

### **1. 🔍 Healthcare Rules Logic:**
```python
def get_healthcare_fallback(col_name, sql_type, values=None):
    col_lower = col_name.lower()
    
    # PRIORITY 1: Check for ID patterns first
    if values and analyze_id_pattern(values):
        return {"suggested_masking": "format_preserving_id"}
    
    # PRIORITY 2: Healthcare-specific patterns
    if any(term in col_lower for term in ['patient_id', 'mrn']):
        return {"suggested_masking": "deterministic_hash"}
    elif any(term in col_lower for term in ['name']):
        return {"suggested_masking": "faker.name()"}
    # ... more healthcare rules
```

**Logic:** Prioritizes ID detection, then applies healthcare-specific rules based on column names and data patterns.

### **2. 🆔 Format-Preserving ID Logic:**
```python
def format_preserving_id_mask(original_id):
    masked_chars = []
    for char in original_id:
        if char.isalpha():
            # Letter → Random letter (same case)
            if char.isupper():
                masked_chars.append(random.choice(string.ascii_uppercase))
            else:
                masked_chars.append(random.choice(string.ascii_lowercase))
        elif char.isdigit():
            # Number → Random number
            masked_chars.append(str(random.randint(0, 9)))
        else:
            # Special character → Keep unchanged
            masked_chars.append(char)
    return ''.join(masked_chars)
```

**Logic:** Preserves the exact format (letters stay letters, numbers stay numbers) while changing the actual values.

### **3. 🧠 Smart ID Pattern Detection:**
```python
def analyze_id_pattern(values):
    # Check length consistency
    lengths = [len(str(v)) for v in values]
    avg_length = sum(lengths) / len(lengths)
    if not (4 <= avg_length <= 25):
        return False
    
    # Check for mix of letters and numbers
    id_indicators = 0
    for val in values:
        has_letters = any(c.isalpha() for c in str(val))
        has_numbers = any(c.isdigit() for c in str(val))
        if has_letters and has_numbers:
            id_indicators += 1
    
    return id_indicators >= len(values) * 0.5  # 50% threshold
```

**Logic:** Analyzes data patterns to distinguish IDs from regular text based on length, character mix, and consistency.

### **4. 🔗 Multi-Table Relationship Logic:**
```python
def auto_detect_relationships(tables_data):
    relationships = []
    for table1, table2 in combinations(tables_data.keys(), 2):
        common_columns = set(df1.columns) & set(df2.columns)
        for col in common_columns:
            if is_likely_key_column(col, df1[col], df2[col]):
                relationships.append({
                    'table1': table1, 'column1': col,
                    'table2': table2, 'column2': col
                })
    return relationships
```

**Logic:** Finds common column names between tables and verifies they contain related data (foreign key relationships).

### **5. 🎯 Cross-Table Masking Logic:**
```python
def apply_cross_table_masking(tables_data, masking_plans, relationships):
    # Create global mappings for consistent masking
    global_mappings = {}
    for rel in relationships:
        # Collect all unique values from both tables
        all_values = set()
        all_values.update(table1_data[col1].unique())
        all_values.update(table2_data[col2].unique())
        
        # Create consistent mapping
        value_mapping = {}
        for unique_val in all_values:
            value_mapping[unique_val] = mask_value(unique_val, method)
        
        global_mappings[rel_key] = value_mapping
    
    # Apply consistent masking to all tables
    for table_name, df in tables_data.items():
        masked_df = apply_masking_with_global_mappings(df, plan, global_mappings)
```

**Logic:** Creates consistent value mappings across related tables so the same foreign key gets the same masked value everywhere.

### **6. 🎛️ Selective Masking Logic:**
```python
def apply_selective_masking(df, plan, row_selection_mode, **options):
    # Determine which rows to mask
    if row_selection_mode == "🔄 Mask All Rows":
        rows_to_mask = list(range(len(df)))
    elif row_selection_mode == "🔢 Mask Specific Number of Rows":
        rows_to_mask = list(range(num_rows))
    elif row_selection_mode == "📍 Mask Specific Row Range":
        rows_to_mask = list(range(start_row, end_row))
    elif row_selection_mode == "🎲 Mask Random Sample":
        rows_to_mask = random.sample(range(len(df)), num_rows)
    
    # Apply masking only to selected rows and columns
    for col_plan in plan:
        if col_plan['suggested_masking'] != "no masking":
            for idx in rows_to_mask:
                df.loc[idx, col] = mask_value(df.loc[idx, col], method)
```

**Logic:** Selectively applies masking to only the rows and columns the user specified.

---

## 🤖 **AI INTEGRATION LOGIC**

### **GPT Prompt Logic:**
```python
def ask_gpt_for_masking(col_name, sql_type, values):
    prompt = f"""Healthcare data masking expert. Analyze: {col_name} ({sql_type}) 
    with samples: {values[:3]}

    CRITICAL ID DETECTION:
    - If samples look like IDs → "format_preserving_id"
    
    OTHER RULES:
    - Patient/doctor names → "faker.name()"
    - Medical record numbers → "deterministic_hash"
    - Birth dates → "hipaa_age_group"
    
    JSON only: {{"logical_type": "string", "suggested_masking": "faker.name()"}}"""
```

**Logic:** Sends column information to GPT with healthcare-specific instructions to get optimal masking suggestions.

---

## 🔄 **COMPLETE WORKFLOW LOGIC**

### **1. Initialization:**
```
User starts app → Database connection → Load available tables
```

### **2. Selection Phase:**
```
User selects mode → User selects table(s) → System loads data & detects relationships
```

### **3. Suggestion Phase:**
```
User chooses AI/Manual → System generates suggestions → User reviews suggestions
```

### **4. Configuration Phase:**
```
User customizes columns → User customizes rows → User sets advanced options
```

### **5. Masking Phase:**
```
System applies selective masking → System preserves relationships → System generates results
```

### **6. Output Phase:**
```
User previews results → User downloads data → User gets masking report
```

---

## 🎯 **KEY ALGORITHMS SUMMARY**

### **🆔 ID Masking Algorithm:**
1. **Detect ID patterns** in data (length, character mix, consistency)
2. **Preserve format** (letters→letters, numbers→numbers)
3. **Maintain prefixes** (CLM, PAT, PROV stay consistent)
4. **Create mappings** for relationship consistency

### **🔗 Relationship Preservation Algorithm:**
1. **Auto-detect relationships** (common columns with related data)
2. **Create global mappings** (same original value → same masked value)
3. **Apply consistently** across all related tables
4. **Verify integrity** (foreign keys remain valid)

### **🎛️ Selective Masking Algorithm:**
1. **Parse user selections** (which columns, which rows)
2. **Create masking plan** (method for each selected element)
3. **Apply selectively** (only to chosen rows/columns)
4. **Preserve unselected** (leave unchanged)

### **🏥 Healthcare Rules Algorithm:**
1. **Analyze column names** for medical patterns
2. **Examine data samples** for content types
3. **Apply HIPAA guidelines** for appropriate masking
4. **Prioritize ID detection** over generic rules

---

## 🎉 **RESULT: Enterprise-Grade Healthcare Data Masking**

Your tool combines all these algorithms to provide:
- ✅ **Intelligent masking** that understands healthcare data
- ✅ **Relationship preservation** for functional masked data
- ✅ **User control** over exactly what gets masked
- ✅ **HIPAA compliance** with complete anonymization
- ✅ **Professional interface** for hospital environments

---

## 🧪 **DETAILED MASKING METHODS LOGIC**

### **📋 All Available Masking Methods:**

#### **🆔 Identity & ID Methods:**
```python
"format_preserving_id"     # CLMEA1A949D → XLMEA2B847F (preserves structure)
"deterministic_hash"       # John → a1b2c3d4... (same input = same output)
"hash_sha256"             # Secure one-way hashing
"hash_md5"                # MD5 hashing (faster, less secure)
"tokenize"                # Replace with random tokens
```

#### **👤 Personal Data Methods:**
```python
"faker.name()"            # John Doe → Jane Smith
"faker.email()"           # <EMAIL> → <EMAIL>
"faker.phone_number()"    # 555-1234 → 555-9876
"faker.address()"         # 123 Main St → 456 Oak Ave
"faker.company()"         # ABC Corp → XYZ Inc
"faker.job()"             # Doctor → Engineer
"faker.date()"            # 1990-01-01 → 1985-05-15
```

#### **🏥 Healthcare-Specific Methods:**
```python
"hipaa_age_group"         # 25 → "18-29" (HIPAA Safe Harbor)
"mask_clinical_notes"     # "Patient has..." → "[CLINICAL_NOTE_MASKED]"
"realistic_vital_signs"   # BP: 120/80 → 118/82 (realistic variance)
"mask_lab_results"        # Glucose: 95 → 92 (clinically realistic)
"generate_mrn"            # → MRN123456789 (realistic medical record numbers)
"generate_patient_id"     # → PAT987654321 (realistic patient IDs)
"generate_insurance_number" # → INS456789123 (realistic insurance numbers)
```

#### **🔢 Numeric Methods:**
```python
"add_noise_percentage"    # 100 → 103 (add 3% noise)
"add_noise_fixed"         # 100 → 105 (add fixed amount)
"scramble"                # 12345 → 54321 (scramble digits)
"binning"                 # 25 → "20-30" (age groups)
"categorical_binning"     # 95 → "Normal" (lab result categories)
```

#### **📅 Date Methods:**
```python
"shift_days_random"       # 2023-01-01 → 2023-01-15 (random shift)
"shift_consistent"        # All dates shifted by same amount
"mask_partial"            # 2023-01-01 → 2023-XX-XX (partial masking)
```

#### **🔤 Text Methods:**
```python
"character_substitution"  # ABC → XYZ (character mapping)
"random_string_same_length" # "Hello" → "Xm9Kp" (same length)
"random_string_fixed_length" # Any text → "ABCD1234" (fixed length)
"substitute_from_list"    # Replace with predefined values
```

---

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS**

### **🗄️ Database Connection Logic:**
```python
def get_connection():
    return psycopg2.connect(
        host=os.getenv("DB_HOST"),
        database=os.getenv("DB_NAME"),
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD")
    )

def get_schema_and_samples(table_name, limit=5):
    conn = get_connection()
    # Get column info
    schema_query = """
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = %s
    """
    # Get sample data
    sample_query = f"SELECT * FROM {table_name} LIMIT {limit}"
```

### **💾 Caching Logic:**
```python
def load_cache():
    try:
        with open("masking_cache.json", "r") as f:
            return json.load(f)
    except:
        return {}

def save_cache(cache):
    with open("masking_cache.json", "w") as f:
        json.dump(cache, f)

# Cache key format: "column_name|sql_type"
cache_key = f"{col_name}|{sql_type}"
```

### **🎲 Random Seed Logic:**
```python
def apply_selective_masking(..., random_seed=None):
    if random_seed is not None:
        random.seed(random_seed)
        np.random.seed(random_seed)

    # Ensures reproducible results for same seed
    rows_to_mask = random.sample(range(total_rows), num_rows)
```

### **📊 Session State Management:**
```python
# Single table mode
st.session_state["single_table_mode"] = True
st.session_state["selected_table"] = table_name
st.session_state["schema"] = schema
st.session_state["df"] = dataframe
st.session_state["masking_plan"] = plan

# Multi-table mode
st.session_state["single_table_mode"] = False
st.session_state["tables_data"] = {table: df, ...}
st.session_state["masking_plans"] = {table: plan, ...}
st.session_state["relationships"] = relationships
st.session_state["masked_tables"] = masked_results
```

---

## 🔍 **ERROR HANDLING & VALIDATION LOGIC**

### **🛡️ Input Validation:**
```python
def validate_masking_input(df, plan):
    # Check if columns exist
    for col_plan in plan:
        if col_plan['column'] not in df.columns:
            raise ValueError(f"Column {col_plan['column']} not found")

    # Check if masking method is valid
    if col_plan['suggested_masking'] not in MASKING_OPTIONS:
        raise ValueError(f"Invalid masking method")

def validate_row_selection(df, start_row, end_row, num_rows):
    total_rows = len(df)
    if start_row < 0 or start_row >= total_rows:
        raise ValueError("Invalid start row")
    if end_row > total_rows:
        raise ValueError("Invalid end row")
    if num_rows > total_rows:
        raise ValueError("Number of rows exceeds dataset size")
```

### **🔄 Fallback Logic:**
```python
def mask_column(value, method, reference_values=None):
    try:
        # Try primary masking method
        return apply_masking_method(value, method, reference_values)
    except Exception as e:
        # Fallback to safe default
        logger.warning(f"Masking failed for {value} with {method}: {e}")
        return "[MASKED]"  # Safe fallback

def get_masking_plan(schema, df, use_gpt=True):
    try:
        if use_gpt:
            return get_gpt_suggestions(schema, df)
    except Exception as e:
        logger.warning(f"GPT failed: {e}")
        # Fallback to healthcare rules
        return get_healthcare_suggestions(schema, df)
```

---

## 📈 **PERFORMANCE OPTIMIZATION LOGIC**

### **⚡ Batch Processing:**
```python
def apply_masking_batch(df, plan, batch_size=1000):
    total_rows = len(df)
    for start_idx in range(0, total_rows, batch_size):
        end_idx = min(start_idx + batch_size, total_rows)
        batch_df = df.iloc[start_idx:end_idx].copy()

        # Apply masking to batch
        masked_batch = apply_masking(batch_df, plan)
        df.iloc[start_idx:end_idx] = masked_batch
```

### **🧠 Memory Management:**
```python
def process_large_dataset(df, plan):
    # Process in chunks to avoid memory issues
    chunk_size = 10000
    for chunk in pd.read_csv(file, chunksize=chunk_size):
        masked_chunk = apply_masking(chunk, plan)
        yield masked_chunk
```

### **💾 Lazy Loading:**
```python
def get_schema_and_samples(table_name, limit=5):
    # Only load sample data, not entire table
    sample_query = f"SELECT * FROM {table_name} LIMIT {limit}"
    # Full data loaded only when masking is applied
```

---

## 🎯 **COMPLETE SYSTEM ARCHITECTURE**

```
┌─────────────────────────────────────────────────────────────┐
│                    🖥️ STREAMLIT UI (app.py)                  │
├─────────────────────────────────────────────────────────────┤
│ Mode Selection → Table Selection → AI/Manual Choice →       │
│ Suggestion Generation → User Customization → Apply Masking  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│              🧠 MASKING ENGINE (masking_engine.py)           │
├─────────────────────────────────────────────────────────────┤
│ • Healthcare Rules Logic                                    │
│ • Format-Preserving ID Masking                            │
│ • Multi-Table Relationship Detection                       │
│ • Cross-Table Consistent Masking                          │
│ • Selective Row/Column Processing                          │
│ • AI Integration (GPT)                                     │
│ • 25+ Masking Methods                                      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   🗄️ DATABASE & STORAGE                     │
├─────────────────────────────────────────────────────────────┤
│ PostgreSQL Database ← → Masking Cache ← → Session State    │
└─────────────────────────────────────────────────────────────┘
```

**This comprehensive logic creates an enterprise-grade healthcare data masking solution that intelligently handles any hospital data scenario while maintaining complete user control and HIPAA compliance!** 🏥🧠🔐✨
