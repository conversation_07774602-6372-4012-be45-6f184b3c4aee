#!/usr/bin/env python3
"""
Test the new selective masking features
"""

import pandas as pd
from masking_engine import apply_selective_masking, generate_masking_report

def test_selective_masking_features():
    """Test all selective masking features"""
    print("🎯 Testing Selective Masking Features")
    print("=" * 45)
    
    # Create test data
    test_data = {
        'claim_id': ['CLMEA1A949D', 'CLMF72F0B6C', 'CLMC90496AA', 'CLMDEAED70E', 'CLM29B7E149'],
        'patient_id': ['PATFD81BF08', 'PAT46843087', 'PAT46843087', 'PAT450BFC2B', 'PAT450BFC2B'],
        'provider_id': ['PROVC934AC56', 'PROV131A6DFE', 'PROV644A1850', 'PROV6A69DFF4', 'PROV84B1613F'],
        'patient_name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'status': ['Active', 'Completed', 'Pending', 'Active', 'Completed']
    }
    
    df = pd.DataFrame(test_data)
    
    # Create masking plan
    masking_plan = [
        {"column": "claim_id", "suggested_masking": "format_preserving_id"},
        {"column": "patient_id", "suggested_masking": "format_preserving_id"},
        {"column": "provider_id", "suggested_masking": "format_preserving_id"},
        {"column": "patient_name", "suggested_masking": "faker.name()"},
        {"column": "status", "suggested_masking": "no masking"}
    ]
    
    print("📊 Original Data:")
    print(df.to_string(index=False))
    print()
    
    return df, masking_plan

def test_row_selection_modes(df, masking_plan):
    """Test different row selection modes"""
    print("📋 Testing Row Selection Modes")
    print("=" * 35)
    
    # Test 1: Mask all rows
    print("1. 🔄 Mask All Rows:")
    result1 = apply_selective_masking(
        df, masking_plan,
        row_selection_mode="🔄 Mask All Rows"
    )
    print(f"   Result: {len(result1)} rows processed")
    
    # Test 2: Mask first 3 rows
    print("2. 🔢 Mask First 3 Rows:")
    result2 = apply_selective_masking(
        df, masking_plan,
        row_selection_mode="🔢 Mask Specific Number of Rows",
        num_rows=3
    )
    print(f"   Result: First 3 rows masked, last 2 unchanged")
    print("   First 3 claim_ids:", result2['claim_id'].head(3).tolist())
    print("   Last 2 claim_ids:", result2['claim_id'].tail(2).tolist())
    
    # Test 3: Mask rows 1-3 (range)
    print("3. 📍 Mask Row Range (1-3):")
    result3 = apply_selective_masking(
        df, masking_plan,
        row_selection_mode="📍 Mask Specific Row Range",
        start_row=1,
        end_row=4
    )
    print(f"   Result: Rows 1-3 masked, row 0 and 4 unchanged")
    print("   All claim_ids:", result3['claim_id'].tolist())
    
    # Test 4: Random sample
    print("4. 🎲 Random Sample (2 rows, seed=42):")
    result4 = apply_selective_masking(
        df, masking_plan,
        row_selection_mode="🎲 Mask Random Sample",
        num_rows=2,
        random_seed=42
    )
    print(f"   Result: 2 random rows masked")
    print("   All claim_ids:", result4['claim_id'].tolist())
    
    return result1, result2, result3, result4

def test_column_selection(df, masking_plan):
    """Test column selection by modifying masking plan"""
    print("\n📋 Testing Column Selection")
    print("=" * 30)
    
    # Test 1: Mask only ID columns (exclude names and status)
    print("1. Mask Only ID Columns:")
    id_only_plan = [
        {"column": "claim_id", "suggested_masking": "format_preserving_id"},
        {"column": "patient_id", "suggested_masking": "format_preserving_id"},
        {"column": "provider_id", "suggested_masking": "format_preserving_id"},
        {"column": "patient_name", "suggested_masking": "no masking"},  # Excluded
        {"column": "status", "suggested_masking": "no masking"}  # Excluded
    ]
    
    result1 = apply_selective_masking(df, id_only_plan)
    print("   IDs masked, names/status unchanged:")
    print("   claim_id:", result1['claim_id'].iloc[0])
    print("   patient_name:", result1['patient_name'].iloc[0])
    
    # Test 2: Mask only names (exclude IDs)
    print("2. Mask Only Names:")
    names_only_plan = [
        {"column": "claim_id", "suggested_masking": "no masking"},  # Excluded
        {"column": "patient_id", "suggested_masking": "no masking"},  # Excluded
        {"column": "provider_id", "suggested_masking": "no masking"},  # Excluded
        {"column": "patient_name", "suggested_masking": "faker.name()"},
        {"column": "status", "suggested_masking": "no masking"}
    ]
    
    result2 = apply_selective_masking(df, names_only_plan)
    print("   Names masked, IDs unchanged:")
    print("   claim_id:", result2['claim_id'].iloc[0])
    print("   patient_name:", result2['patient_name'].iloc[0])
    
    return result1, result2

def test_advanced_options(df, masking_plan):
    """Test advanced masking options"""
    print("\n🎛️ Testing Advanced Options")
    print("=" * 30)
    
    # Test 1: Preserve relationships
    print("1. Preserve Relationships (same patient_id gets same masked value):")
    result1 = apply_selective_masking(
        df, masking_plan,
        preserve_relationships=True
    )
    # Check if duplicate patient_ids get same masked values
    pat_ids = result1['patient_id'].tolist()
    print(f"   Original: {df['patient_id'].tolist()}")
    print(f"   Masked:   {pat_ids}")
    print(f"   Consistent: {pat_ids[1] == pat_ids[2] and pat_ids[3] == pat_ids[4]}")
    
    # Test 2: Include original columns
    print("2. Include Original Columns:")
    result2 = apply_selective_masking(
        df, masking_plan,
        include_original=True
    )
    print(f"   Columns: {list(result2.columns)}")
    print(f"   Has originals: {'claim_id_original' in result2.columns}")
    
    # Test 3: Add masking indicators
    print("3. Add Masking Indicators:")
    result3 = apply_selective_masking(
        df, masking_plan,
        add_mask_indicators=True
    )
    print(f"   Columns: {list(result3.columns)}")
    print(f"   Has indicators: {'claim_id_mask_method' in result3.columns}")
    if 'claim_id_mask_method' in result3.columns:
        print(f"   Indicator value: {result3['claim_id_mask_method'].iloc[0]}")
    
    return result1, result2, result3

def test_masking_report(df, masked_df, masking_plan):
    """Test masking report generation"""
    print("\n📋 Testing Masking Report Generation")
    print("=" * 40)
    
    # Create mock session state
    session_state = {
        "row_selection_mode": "🔢 Mask Specific Number of Rows",
        "num_rows": 3,
        "preserve_relationships": True,
        "include_original": False,
        "add_mask_indicators": False
    }
    
    report = generate_masking_report(df, masked_df, masking_plan, session_state)
    
    # Show first few lines of report
    report_lines = report.split('\n')
    print("Report preview (first 15 lines):")
    for i, line in enumerate(report_lines[:15]):
        print(f"   {line}")
    
    print(f"\n   Full report length: {len(report_lines)} lines")
    print(f"   Report size: {len(report)} characters")
    
    return report

def main():
    """Run all selective masking tests"""
    print("🎯 Comprehensive Selective Masking Test")
    print("=" * 50)
    
    try:
        # Setup
        df, masking_plan = test_selective_masking_features()
        
        # Test row selection
        results = test_row_selection_modes(df, masking_plan)
        
        # Test column selection
        col_results = test_column_selection(df, masking_plan)
        
        # Test advanced options
        adv_results = test_advanced_options(df, masking_plan)
        
        # Test report generation
        report = test_masking_report(df, results[0], masking_plan)
        
        print("\n" + "=" * 50)
        print("🎉 ALL SELECTIVE MASKING TESTS PASSED!")
        
        print("\n🚀 New Features Available:")
        print("✅ Row Selection:")
        print("   • Mask all rows")
        print("   • Mask first N rows")
        print("   • Mask specific row range")
        print("   • Mask random sample")
        
        print("✅ Column Selection:")
        print("   • Select specific columns to mask")
        print("   • Exclude specific columns from masking")
        print("   • Mask all columns")
        
        print("✅ Advanced Options:")
        print("   • Preserve data relationships")
        print("   • Include original columns for comparison")
        print("   • Add masking method indicators")
        print("   • Configurable preview rows")
        
        print("✅ Enhanced Output:")
        print("   • Detailed masking statistics")
        print("   • Comprehensive masking reports")
        print("   • Multiple download options")
        
        print("\n🏥 Perfect for Hospital Use Cases:")
        print("• 🧪 Test environments (mask subset of data)")
        print("• 🎓 Training (mask only sensitive columns)")
        print("• 📊 Analytics (preserve relationships)")
        print("• 🔍 Auditing (detailed reports)")
        
        print("\n🎯 Your Streamlit app now has:")
        print("• Complete control over what gets masked")
        print("• Flexible row and column selection")
        print("• Professional reporting capabilities")
        print("• Enterprise-grade customization options")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
