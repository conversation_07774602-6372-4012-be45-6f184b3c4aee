#!/usr/bin/env python3
"""
Debug the masking plan generation step by step
"""

import pandas as pd
from masking_engine import get_healthcare_fallback, is_complex_case, analyze_id_pattern

def debug_patient_id_issue():
    """Debug why patient_id is not getting format_preserving_id"""
    print("🔍 Debugging patient_id Issue")
    print("=" * 35)
    
    col_name = "patient_id"
    sql_type = "varchar"
    values = ["PATFD81BF08", "PAT46843087", "PAT450BFC2B"]
    
    print(f"Column: {col_name}")
    print(f"SQL Type: {sql_type}")
    print(f"Values: {values}")
    
    # Step 1: Check ID pattern analysis
    print(f"\n1. ID Pattern Analysis:")
    is_id = analyze_id_pattern(values)
    print(f"   analyze_id_pattern(values) = {is_id}")
    
    # Step 2: Check complex case detection
    print(f"\n2. Complex Case Detection:")
    is_complex = is_complex_case(col_name, values)
    print(f"   is_complex_case(col_name, values) = {is_complex}")
    
    # Step 3: Check healthcare fallback step by step
    print(f"\n3. Healthcare Fallback Logic:")
    col_lower = col_name.lower()
    print(f"   col_lower = '{col_lower}'")
    
    # Check each condition in order
    id_keywords = ['id', 'number', 'code', 'claim', 'member', 'account', 'provider', 'patient', 'mrn']
    has_id_keyword = any(keyword in col_lower for keyword in id_keywords)
    print(f"   has_id_keyword = {has_id_keyword}")
    
    if has_id_keyword:
        print(f"   ✅ Has ID keyword")
        if values and analyze_id_pattern(values):
            print(f"   ✅ Values look like IDs")
            print(f"   → Should return: format_preserving_id")
        else:
            print(f"   ❌ Values don't look like IDs")
            
            # Check the specific patient_id condition
            patient_terms = ['patient_id', 'mrn', 'medical_record']
            has_patient_term = any(term in col_lower for term in patient_terms)
            print(f"   has_patient_term = {has_patient_term}")
            
            if has_patient_term and values and not analyze_id_pattern(values):
                print(f"   → Would return: deterministic_hash")
            else:
                print(f"   → Would continue to next check")
    
    # Step 4: Call the actual function
    print(f"\n4. Actual Function Result:")
    result = get_healthcare_fallback(col_name, sql_type, values)
    print(f"   Result: {result}")
    
    # Step 5: Analyze the issue
    print(f"\n5. Issue Analysis:")
    if result['suggested_masking'] != 'format_preserving_id':
        print(f"   ❌ Expected: format_preserving_id")
        print(f"   ❌ Got: {result['suggested_masking']}")
        print(f"   🔍 The issue is likely in the healthcare fallback logic")
    else:
        print(f"   ✅ Correct result!")

def debug_all_columns():
    """Debug all three columns"""
    print("\n🔍 Debugging All Columns")
    print("=" * 30)
    
    test_cases = [
        ("claim_id", ["CLMEA1A949D", "CLMF72F0B6C", "CLMC90496AA"]),
        ("patient_id", ["PATFD81BF08", "PAT46843087", "PAT450BFC2B"]),
        ("provider_id", ["PROVC934AC56", "PROV131A6DFE", "PROV644A1850"])
    ]
    
    for col_name, values in test_cases:
        print(f"\n--- {col_name} ---")
        
        # Check ID pattern
        is_id = analyze_id_pattern(values)
        print(f"ID Pattern: {is_id}")
        
        # Check healthcare rules
        result = get_healthcare_fallback(col_name, "varchar", values)
        method = result['suggested_masking']
        logical_type = result['logical_type']
        
        expected = "format_preserving_id"
        status = "✅" if method == expected else "❌"
        
        print(f"Suggestion: {method} ({logical_type}) {status}")
        
        if method != expected:
            print(f"❌ ISSUE: Expected {expected}, got {method}")

def main():
    print("🐛 Debug Masking Plan Generation")
    print("=" * 40)
    
    debug_patient_id_issue()
    debug_all_columns()

if __name__ == "__main__":
    main()
