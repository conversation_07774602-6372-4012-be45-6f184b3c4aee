#!/usr/bin/env python3
"""
Test with force_refresh to bypass cache
"""

import pandas as pd
from masking_engine import get_masking_plan

def test_force_refresh():
    """Test with force_refresh=True to bypass cache"""
    print("🔄 Testing with force_refresh=True")
    print("=" * 40)
    
    # Your exact data
    your_data = {
        'claim_id': ['CLMEA1A949D', 'CLMF72F0B6C', 'CLMC90496AA'],
        'patient_id': ['PATFD81BF08', 'PAT46843087', 'PAT450BFC2B'], 
        'provider_id': ['PROVC934AC56', 'PROV131A6DFE', 'PROV644A1850']
    }
    
    df = pd.DataFrame(your_data)
    schema = [(col, 'varchar') for col in df.columns]
    
    # Test with force_refresh=True
    print("⚡ Manual Mode with force_refresh=True:")
    plan = get_masking_plan(schema, df, use_gpt=False, force_refresh=True)
    
    all_correct = True
    for plan_item in plan:
        col = plan_item['column']
        method = plan_item['suggested_masking']
        logical_type = plan_item['logical_type']
        
        expected_method = "format_preserving_id"
        expected_type = "structured_id"
        
        method_ok = method == expected_method
        type_ok = logical_type == expected_type
        
        if not (method_ok and type_ok):
            all_correct = False
        
        method_status = "✅" if method_ok else "❌"
        type_status = "✅" if type_ok else "❌"
        
        print(f"  {col}: {method} {method_status} | {logical_type} {type_status}")
    
    if all_correct:
        print("\n🎉 SUCCESS! All ID columns correctly detected with force_refresh!")
        print("✅ format_preserving_id suggested for all ID columns")
        print("✅ Cache was the issue - now bypassed")
    else:
        print("\n⚠️ Still issues even with force_refresh")
        
    return all_correct

def main():
    success = test_force_refresh()
    
    if success:
        print("\n🎯 Solution Found!")
        print("The issue was stale cache entries.")
        print("Your Streamlit app will work correctly now.")
        print("\n💡 To ensure fresh results in your app:")
        print("1. Clear cache periodically")
        print("2. Or use force_refresh=True for critical columns")
        print("3. Or add cache versioning")
    else:
        print("\n🔍 Need to investigate further...")

if __name__ == "__main__":
    main()
