# Comprehensive Data Masking Options Guide

This guide provides a complete overview of all available data masking options in your enhanced data masking tool, organized by data type.

## 📝 TEXT/STRING Data Types

### Personal Identifiers
- **`faker.name()`** - Generate realistic fake names
- **`faker.first_name()`** - Generate fake first names only
- **`faker.last_name()`** - Generate fake last names only
- **`faker.email()`** - Generate fake email addresses
- **`faker.phone_number()`** - Generate fake phone numbers
- **`faker.ssn()`** - Generate fake social security numbers
- **`faker.license_plate()`** - Generate fake license plates
- **`faker.passport_number()`** - Generate fake passport numbers

### Address & Location
- **`faker.address()`** - Generate complete fake addresses
- **`faker.street_address()`** - Generate fake street addresses
- **`faker.city()`** - Generate fake city names
- **`faker.state()`** - Generate fake state names
- **`faker.country()`** - Generate fake country names
- **`faker.zipcode()`** - Generate fake zip codes
- **`faker.latitude()`** - Generate fake latitude coordinates
- **`faker.longitude()`** - Generate fake longitude coordinates

### Business & Professional
- **`faker.company()`** - Generate fake company names
- **`faker.job()`** - Generate fake job titles
- **`faker.catch_phrase()`** - Generate fake business slogans
- **`faker.bs()`** - Generate fake business speak

### Text Transformation Methods
- **`hash_sha256`** - SHA-256 hash (irreversible, most secure)
- **`hash_md5`** - MD5 hash (irreversible, faster but less secure)
- **`hash_sha1`** - SHA-1 hash (irreversible, medium security)
- **`scramble`** - Scramble characters within text randomly
- **`shuffle_words`** - Shuffle words in sentences
- **`mask_out`** - Replace entire string with asterisks (****)
- **`mask_partial`** - Mask middle characters (Jo***hn)
- **`mask_first_half`** - Mask first half of string
- **`mask_last_half`** - Mask last half of string
- **`redact`** - Replace with [REDACTED]
- **`nullify`** - Set to NULL/None
- **`tokenize`** - Replace with unique random tokens
- **`encrypt_aes`** - AES encryption (reversible with key)
- **`deterministic_hash`** - Same input always produces same output
- **`random_string_same_length`** - Random string of same length
- **`random_string_fixed_length`** - Random string of fixed length (8 chars)
- **`substitute_from_list`** - Replace with values from existing data
- **`character_substitution`** - Replace specific characters (a→@, e→3, etc.)

## 🔢 NUMERIC Data Types

### Faker Numeric Data
- **`faker.random_int()`** - Random integers
- **`faker.random_number()`** - Random numbers with varying digits
- **`faker.pyfloat()`** - Random floating point numbers
- **`faker.credit_card_number()`** - Fake credit card numbers

### Mathematical Transformations
- **`add_noise_percentage`** - Add ±10% random noise to values
- **`add_noise_fixed`** - Add ±10 fixed amount noise
- **`multiply_by_factor`** - Multiply by random factor (0.8-1.2)
- **`round_to_nearest_10`** - Round to nearest 10
- **`round_to_nearest_100`** - Round to nearest 100
- **`round_to_nearest_1000`** - Round to nearest 1000
- **`binning`** - Group into ranges (0-24, 25-49, 50-74, 75-99, 100+)
- **`rank_transformation`** - Replace with percentile rank (P25, P50, etc.)

### Range-Based Methods
- **`random_in_range`** - Random number within min-max of original data
- **`random_in_percentile_range`** - Random within percentile bounds
- **`categorical_binning`** - Convert to categories (Low, Medium, High)

### Preservation Methods
- **`maintain_distribution`** - Keep statistical distribution similar
- **`variance`** - Add small random variance (±10% of value)

## 📅 DATE/DATETIME Data Types

### Faker Date Methods
- **`faker.date_of_birth()`** - Generate realistic birth dates
- **`faker.date_time()`** - Generate random datetime
- **`faker.date_time_this_decade()`** - Dates within this decade
- **`faker.date_time_this_year()`** - Dates within this year
- **`faker.date_time_this_month()`** - Dates within this month
- **`faker.date_time_this_century()`** - Dates within this century
- **`faker.date_time_between()`** - Dates in specified range
- **`faker.future_date()`** - Future dates
- **`faker.past_date()`** - Past dates

### Date Shifting Methods
- **`shift_days_random`** - Shift by random ±30 days
- **`shift_months_random`** - Shift by random ±6 months
- **`shift_years_random`** - Shift by random ±2 years
- **`shift_days_fixed`** - Shift by fixed 30 days
- **`shift_consistent`** - Same 30-day shift for all records

### Date Masking Methods
- **`year_only`** - Keep only year (set to January 1st)
- **`month_year_only`** - Keep only month/year (set to 1st of month)
- **`quarter_year_only`** - Keep only quarter/year (Q1 2023)
- **`day_of_week_only`** - Keep only day of week (Monday, Tuesday, etc.)
- **`season_only`** - Keep only season (Spring, Summer, Fall, Winter)
- **`age_group`** - Convert birth dates to age groups (18-24, 25-34, etc.)

### Time Component Methods
- **`remove_time_component`** - Keep only date part
- **`remove_date_component`** - Keep only time part
- **`round_to_hour`** - Round to nearest hour
- **`round_to_day`** - Round to nearest day (midnight)
- **`randomize_time_component`** - Keep date, randomize time

## ✅ BOOLEAN Data Types
- **`random_boolean`** - Generate random true/false
- **`flip_percentage`** - Flip 10% of boolean values
- **`maintain_boolean_distribution`** - Keep same true/false ratio as original

## 🔧 SPECIAL/COMPOSITE Data Types

### JSON Data
- **`mask_json_fields`** - Mask specific fields (name, email, phone, address)
- **`scramble_json_values`** - Scramble all string values in JSON
- **`hash_json_object`** - Hash entire JSON object

### Array/List Data
- **`shuffle_array`** - Shuffle array elements randomly
- **`random_array_same_size`** - Generate random array of same size

## 🚀 ADVANCED TECHNIQUES

### Format-Preserving & Privacy
- **`format_preserving_encrypt`** - Encrypt while maintaining format
- **`laplace_noise`** - Add Laplace noise for differential privacy
- **`gaussian_noise`** - Add Gaussian noise for differential privacy
- **`synthetic_similar`** - Generate synthetic data with similar properties

## 🔄 LEGACY/BACKWARD COMPATIBILITY
- **`hash`** - SHA-256 hash (same as hash_sha256)
- **`encrypt`** - MD5 hash (same as hash_md5)
- **`substitute`** - Random substitution from existing values
- **`shuffle`** - Shuffle entire column values
- **`no masking`** - Keep original values unchanged

## 💡 Usage Tips

### Choosing the Right Method

**For Personal Data (Names, Emails, Phones):**
- Use `faker.*` methods for realistic replacements
- Use `hash_sha256` for irreversible anonymization
- Use `mask_partial` to keep some recognizable pattern

**For Numeric Data (IDs, Amounts, Counts):**
- Use `add_noise_percentage` to maintain relative relationships
- Use `binning` or `categorical_binning` for privacy while preserving trends
- Use `random_in_range` to maintain data distribution

**For Dates (Birth Dates, Transaction Dates):**
- Use `shift_days_random` to maintain temporal relationships
- Use `year_only` or `quarter_year_only` for time-series analysis
- Use `age_group` for demographic analysis without exact dates

**For Sensitive Text:**
- Use `redact` for complete removal
- Use `scramble` to make unreadable but keep length
- Use `character_substitution` for partial obfuscation

### Performance Considerations
- Hash methods (`hash_sha256`, `hash_md5`) are fast and deterministic
- Faker methods are slower but produce more realistic data
- Simple transformations (`mask_out`, `scramble`) are fastest
- Advanced methods (`laplace_noise`, `synthetic_similar`) are slower but more sophisticated

### Security Levels
- **High Security:** `hash_sha256`, `encrypt_aes`, `redact`, `nullify`
- **Medium Security:** `hash_md5`, `scramble`, `tokenize`, `mask_partial`
- **Low Security:** `mask_out`, `character_substitution`, `shuffle_words`
- **Reversible:** `encrypt_aes` (with key), `shift_*` methods, `faker.*` methods
