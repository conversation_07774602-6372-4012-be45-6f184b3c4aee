#!/usr/bin/env python3
"""
Clear cache and test the masking plan generation
"""

import pandas as pd
import os
from masking_engine import get_masking_plan, get_healthcare_fallback

def clear_cache():
    """Clear the masking cache"""
    cache_file = "masking_cache.json"
    if os.path.exists(cache_file):
        os.remove(cache_file)
        print("✅ Cache cleared")
    else:
        print("ℹ️ No cache file found")

def test_direct_healthcare_rules():
    """Test healthcare rules directly"""
    print("🏥 Testing Healthcare Rules Directly")
    print("=" * 40)
    
    test_cases = [
        ("claim_id", "varchar", ["CLMEA1A949D", "CLMF72F0B6C", "CLMC90496AA"]),
        ("patient_id", "varchar", ["PATFD81BF08", "PAT46843087", "PAT450BFC2B"]),
        ("provider_id", "varchar", ["PROVC934AC56", "PROV131A6DFE", "PROV644A1850"])
    ]
    
    for col_name, sql_type, values in test_cases:
        result = get_healthcare_fallback(col_name, sql_type, values)
        method = result['suggested_masking']
        logical_type = result['logical_type']
        
        expected = "format_preserving_id"
        status = "✅" if method == expected else "❌"
        
        print(f"{col_name}: {method} ({logical_type}) {status}")

def test_masking_plan_fresh():
    """Test masking plan with fresh cache"""
    print("\n🔄 Testing Masking Plan (Fresh Cache)")
    print("=" * 40)
    
    # Your exact data
    your_data = {
        'claim_id': ['CLMEA1A949D', 'CLMF72F0B6C', 'CLMC90496AA'],
        'patient_id': ['PATFD81BF08', 'PAT46843087', 'PAT450BFC2B'], 
        'provider_id': ['PROVC934AC56', 'PROV131A6DFE', 'PROV644A1850']
    }
    
    df = pd.DataFrame(your_data)
    schema = [(col, 'varchar') for col in df.columns]
    
    # Test Manual Mode (should use healthcare rules)
    print("⚡ Manual Mode (use_gpt=False):")
    manual_plan = get_masking_plan(schema, df, use_gpt=False)
    
    all_correct = True
    for plan_item in manual_plan:
        col = plan_item['column']
        method = plan_item['suggested_masking']
        logical_type = plan_item['logical_type']
        
        expected_method = "format_preserving_id"
        expected_type = "structured_id"
        
        method_ok = method == expected_method
        type_ok = logical_type == expected_type
        
        if not (method_ok and type_ok):
            all_correct = False
        
        method_status = "✅" if method_ok else "❌"
        type_status = "✅" if type_ok else "❌"
        
        print(f"  {col}: {method} {method_status} | {logical_type} {type_status}")
    
    return all_correct

def main():
    print("🧹 Cache Clear and Fresh Test")
    print("=" * 35)
    
    # Clear cache first
    clear_cache()
    
    # Test healthcare rules directly
    test_direct_healthcare_rules()
    
    # Test full pipeline
    success = test_masking_plan_fresh()
    
    if success:
        print("\n🎉 SUCCESS! All ID columns correctly detected!")
        print("✅ format_preserving_id suggested for all ID columns")
        print("✅ Your Streamlit app should now work correctly")
        print("\n🚀 Ready to use:")
        print("1. Run: streamlit run app.py")
        print("2. Select your table")
        print("3. Choose Manual Mode")
        print("4. All ID columns should suggest format_preserving_id")
    else:
        print("\n⚠️ Still some issues - investigating...")

if __name__ == "__main__":
    main()
