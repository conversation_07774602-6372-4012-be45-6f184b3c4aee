from masking_engine import get_schema_and_samples, get_masking_plan, apply_masking
import pandas as pd

TABLE_NAME = "your_table_name"  # ✅ Change this to your actual table

# Step 1: Get schema + samples
schema, df = get_schema_and_samples(TABLE_NAME)

# Step 2: Get masking plan (cached if available)
plan = get_masking_plan(schema, df)

# Step 3: Apply masking
masked_df = apply_masking(df.copy(), plan)

# Step 4: Save or show result
print(masked_df.head())

# Optionally: Save to new table or CSV
masked_df.to_csv(f"{TABLE_NAME}_masked.csv", index=False)
