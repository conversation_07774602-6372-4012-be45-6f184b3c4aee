#!/usr/bin/env python3
"""
Test script for the hybrid AI/Manual masking approach
"""

import pandas as pd
from masking_engine import get_masking_plan, get_healthcare_fallback

def test_healthcare_rules():
    """Test the healthcare-specific fallback rules"""
    print("🏥 Testing Healthcare Fallback Rules")
    print("=" * 50)
    
    # Test cases: (column_name, sql_type, expected_masking)
    test_cases = [
        # Patient Identifiers
        ("patient_id", "varchar", "deterministic_hash"),
        ("mrn", "varchar", "deterministic_hash"),
        ("medical_record_number", "varchar", "deterministic_hash"),
        ("patient_name", "varchar", "faker.name()"),
        ("first_name", "varchar", "faker.name()"),
        ("last_name", "varchar", "faker.name()"),
        
        # Medical Data
        ("doctor_name", "varchar", "faker.name()"),
        ("attending_physician", "varchar", "faker.name()"),
        ("clinical_notes", "text", "mask_clinical_notes"),
        ("diagnosis", "text", "mask_clinical_notes"),
        ("lab_result", "numeric", "mask_lab_results"),
        ("test_value", "decimal", "mask_lab_results"),
        ("vital_sign", "numeric", "realistic_vital_signs"),
        ("blood_pressure", "varchar", "realistic_vital_signs"),
        
        # Dates
        ("birth_date", "date", "hipaa_age_group"),
        ("date_of_birth", "date", "hipaa_age_group"),
        ("appointment_date", "datetime", "shift_days_random"),
        ("visit_date", "date", "shift_days_random"),
        ("collection_date", "datetime", "shift_consistent"),
        ("lab_date", "date", "shift_consistent"),
        
        # Contact Info
        ("phone_number", "varchar", "faker.phone_number()"),
        ("mobile", "varchar", "faker.phone_number()"),
        ("email", "varchar", "faker.email()"),
        ("address", "text", "faker.address()"),
        
        # Financial/Insurance
        ("insurance_number", "varchar", "hash_sha256"),
        ("ssn", "varchar", "hash_sha256"),
        ("social_security", "varchar", "hash_sha256"),
        ("account_number", "varchar", "tokenize"),
        
        # Medical Codes (should not be masked)
        ("icd_code", "varchar", "no masking"),
        ("procedure_code", "varchar", "no masking"),
        ("test_code", "varchar", "no masking"),
        ("status", "varchar", "no masking"),
        ("specimen_type", "varchar", "no masking"),
    ]
    
    passed = 0
    failed = 0
    
    for col_name, sql_type, expected in test_cases:
        result = get_healthcare_fallback(col_name, sql_type)
        actual = result["suggested_masking"]
        
        if actual == expected:
            print(f"✅ {col_name} ({sql_type}) → {actual}")
            passed += 1
        else:
            print(f"❌ {col_name} ({sql_type}) → Expected: {expected}, Got: {actual}")
            failed += 1
    
    print(f"\n📊 Results: {passed} passed, {failed} failed")
    return failed == 0

def test_hybrid_mode():
    """Test the hybrid AI/Manual mode"""
    print("\n🤖 Testing Hybrid AI/Manual Mode")
    print("=" * 40)
    
    # Create sample hospital data
    sample_data = {
        'patient_id': ['PT001', 'PT002', 'PT003'],
        'patient_name': ['John Doe', 'Jane Smith', 'Bob Johnson'],
        'birth_date': ['1985-03-15', '1992-07-22', '1978-11-08'],
        'lab_result': [85.5, 180.2, 4.2],
        'test_code': ['8061', '83036', '8061'],
        'collection_date': ['2023-05-17', '2025-01-17', '2023-06-15'],
        'status': ['Completed', 'Pending', 'Completed']
    }
    
    df = pd.DataFrame(sample_data)
    schema = [(col, 'varchar' if col in ['patient_id', 'patient_name', 'test_code', 'status'] 
               else 'date' if 'date' in col else 'numeric') for col in df.columns]
    
    print("📋 Sample Data Schema:")
    for col, sql_type in schema:
        print(f"  {col} ({sql_type})")
    
    # Test Manual Mode (Healthcare Rules Only)
    print("\n⚡ Testing Manual Mode (Healthcare Rules):")
    manual_plan = get_masking_plan(schema, df, use_gpt=False)
    
    for plan_item in manual_plan:
        col = plan_item['column']
        method = plan_item['suggested_masking']
        logical_type = plan_item['logical_type']
        print(f"  {col} → {method} (type: {logical_type})")
    
    # Test AI Mode (would use GPT if API key available)
    print("\n🤖 Testing AI Mode (GPT + Healthcare Rules):")
    try:
        ai_plan = get_masking_plan(schema, df, use_gpt=True)
        
        for plan_item in ai_plan:
            col = plan_item['column']
            method = plan_item['suggested_masking']
            logical_type = plan_item['logical_type']
            print(f"  {col} → {method} (type: {logical_type})")
            
    except Exception as e:
        print(f"  ⚠️ AI mode failed (likely no API key): {e}")
        print("  📝 This is expected if OpenAI API key is not configured")
        print("  🔄 Falling back to healthcare rules automatically")

def test_cost_optimization():
    """Test cost optimization features"""
    print("\n💰 Testing Cost Optimization")
    print("=" * 30)
    
    from masking_engine import is_complex_case
    
    # Simple cases (should not use GPT)
    simple_cases = [
        ("patient_id", ["PT001", "PT002"]),
        ("first_name", ["John", "Jane"]),
        ("phone", ["555-1234", "555-5678"]),
        ("birth_date", ["1985-03-15", "1992-07-22"]),
        ("status", ["Active", "Inactive"])
    ]
    
    # Complex cases (should use GPT)
    complex_cases = [
        ("custom_field_xyz", ["Complex data with multiple formats", "Another complex entry"]),
        ("json_data", ['{"key": "value", "nested": {"data": "here"}}', '{"other": "format"}']),
        ("unknown_column", ["http://example.com/data", "https://other.url"])
    ]
    
    print("📊 Simple Cases (should NOT use GPT):")
    for col_name, values in simple_cases:
        is_complex = is_complex_case(col_name, values)
        status = "❌ Would use GPT" if is_complex else "✅ Uses rules only"
        print(f"  {col_name}: {status}")
    
    print("\n🧠 Complex Cases (should use GPT):")
    for col_name, values in complex_cases:
        is_complex = is_complex_case(col_name, values)
        status = "✅ Would use GPT" if is_complex else "❌ Uses rules only"
        print(f"  {col_name}: {status}")

def main():
    """Run all hybrid masking tests"""
    print("🔬 Testing Hybrid AI/Manual Healthcare Data Masking")
    print("=" * 60)
    
    try:
        # Test healthcare rules
        rules_passed = test_healthcare_rules()
        
        # Test hybrid mode
        test_hybrid_mode()
        
        # Test cost optimization
        test_cost_optimization()
        
        print("\n" + "=" * 60)
        if rules_passed:
            print("✅ All tests completed successfully!")
            print("🏥 Your hybrid healthcare masking system is ready!")
            print("\n🎯 Benefits:")
            print("  ⚡ Fast healthcare rules for common cases")
            print("  🤖 AI intelligence for complex cases")
            print("  💰 Cost-optimized GPT usage")
            print("  🔒 HIPAA-compliant suggestions")
        else:
            print("⚠️ Some tests failed - please review the results above")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
