#!/usr/bin/env python3
"""
Simple demo of multi-table masking feature
"""

import pandas as pd
from masking_engine import auto_detect_relationships, apply_cross_table_masking

def demo_your_scenario():
    """Demo your exact scenario from the screenshot"""
    print("🏥 Demo: Your Hospital Claim Scenario")
    print("=" * 45)
    
    # Your exact data from screenshot (using strings for IDs to enable masking)
    claim_data = {
        'ClaimId': ['100', '101'],
        'ClaimDate': [None, None],
        'ClaimAmount': [3993, 3883]
    }

    claim_detail_data = {
        'ClaimId': ['100', '100', '101'],  # Same ClaimIds as in Claim table
        'DiagCode': ['abc', 'cde', 'abc']
    }

    # Test data section
    test_claim_data = {
        'ClaimId': ['888', '999'],
        'ClaimDate': [None, None],
        'ClaimAmount': [3993, 3883]
    }

    test_claim_detail_data = {
        'ClaimId': ['888', '888', '999'],  # Same ClaimIds as in TestClaim table
        'DiagCode': ['abc', 'cde', 'abc']
    }
    
    tables_data = {
        'Claim': pd.DataFrame(claim_data),
        'ClaimDetail': pd.DataFrame(claim_detail_data),
        'TestClaim': pd.DataFrame(test_claim_data),
        'TestClaimDetail': pd.DataFrame(test_claim_detail_data)
    }
    
    print("📊 BEFORE Masking:")
    for table_name, df in tables_data.items():
        print(f"\n{table_name}:")
        print(df.to_string(index=False))
    
    # Auto-detect relationships
    print(f"\n🔗 Auto-detecting relationships...")
    relationships = auto_detect_relationships(tables_data)
    print(f"Found {len(relationships)} relationships:")
    for rel in relationships:
        print(f"  • {rel['table1']}.{rel['column1']} ↔ {rel['table2']}.{rel['column2']}")
    
    # Create masking plans
    masking_plans = {
        'Claim': [
            {"column": "ClaimId", "suggested_masking": "format_preserving_id"},
            {"column": "ClaimDate", "suggested_masking": "no masking"},
            {"column": "ClaimAmount", "suggested_masking": "add_noise_percentage"}
        ],
        'ClaimDetail': [
            {"column": "ClaimId", "suggested_masking": "format_preserving_id"},
            {"column": "DiagCode", "suggested_masking": "no masking"}
        ],
        'TestClaim': [
            {"column": "ClaimId", "suggested_masking": "format_preserving_id"},
            {"column": "ClaimDate", "suggested_masking": "no masking"},
            {"column": "ClaimAmount", "suggested_masking": "add_noise_percentage"}
        ],
        'TestClaimDetail': [
            {"column": "ClaimId", "suggested_masking": "format_preserving_id"},
            {"column": "DiagCode", "suggested_masking": "no masking"}
        ]
    }
    
    print(f"\n🎭 Applying cross-table masking...")
    try:
        masked_tables, mappings = apply_cross_table_masking(
            tables_data, 
            masking_plans, 
            relationships,
            preserve_relationships=True
        )
        
        print("\n📊 AFTER Masking:")
        for table_name, masked_df in masked_tables.items():
            print(f"\n{table_name}:")
            print(masked_df.to_string(index=False))
        
        # Verify your requirement
        print(f"\n✅ VERIFICATION - Your Requirement:")
        print("When ClaimId changes in one table, related tables get the SAME masked value")
        
        # Check ClaimId '100'
        original_100 = '100'

        # Find masked value in Claim table
        claim_masked_100 = None
        for idx, row in masked_tables['Claim'].iterrows():
            if tables_data['Claim'].iloc[idx]['ClaimId'] == '100':
                claim_masked_100 = row['ClaimId']
                break

        # Find masked values in ClaimDetail table
        detail_masked_100 = []
        for idx, row in masked_tables['ClaimDetail'].iterrows():
            if tables_data['ClaimDetail'].iloc[idx]['ClaimId'] == '100':
                detail_masked_100.append(row['ClaimId'])
        
        print(f"\nClaimId 100 Results:")
        print(f"  Claim table: 100 → {claim_masked_100}")
        print(f"  ClaimDetail table: 100 → {detail_masked_100}")
        
        # Check consistency
        if claim_masked_100 and detail_masked_100:
            all_same = all(val == claim_masked_100 for val in detail_masked_100)
            print(f"  Consistency: {'✅ PERFECT!' if all_same else '❌ FAILED'}")
            
            if all_same:
                print(f"\n🎉 SUCCESS! Your feature works perfectly!")
                print(f"✅ ClaimId 100 in Claim table → {claim_masked_100}")
                print(f"✅ ClaimId 100 in ClaimDetail table → {claim_masked_100} (SAME!)")
                print(f"✅ Referential integrity preserved!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during masking: {e}")
        return False

def main():
    """Run the demo"""
    print("🔗 Multi-Table Masking Demo")
    print("=" * 30)
    
    success = demo_your_scenario()
    
    if success:
        print(f"\n" + "=" * 50)
        print("🎯 YOUR FEATURE IS READY!")
        print("=" * 50)
        
        print("\n🏥 What You Asked For:")
        print("✅ Two tables: Claim and ClaimDetail")
        print("✅ Both have ClaimId as primary/foreign key")
        print("✅ When ClaimId changes in one table...")
        print("✅ The same ClaimId in other table gets SAME masked value!")
        
        print("\n🚀 How to Use in Your Streamlit App:")
        print("1. Run: streamlit run app.py")
        print("2. Choose: '🔗 Multi-Table Masking'")
        print("3. Select: Claim and ClaimDetail tables")
        print("4. Tool automatically detects ClaimId relationship")
        print("5. Apply masking → consistent values across tables!")
        
        print("\n🎭 Example Results:")
        print("• Claim.ClaimId: 100 → ABC123XYZ")
        print("• ClaimDetail.ClaimId: 100 → ABC123XYZ (SAME!)")
        print("• Database relationships maintained")
        print("• Perfect for testing/development!")
        
    else:
        print("\n⚠️ Demo encountered issues - check the implementation")

if __name__ == "__main__":
    main()
