#!/usr/bin/env python3
"""
Simple test of the ID masking fix
"""

import pandas as pd
from masking_engine import get_masking_plan

def main():
    print("🧪 Simple ID Masking Test")
    print("=" * 30)
    
    # Your data
    df = pd.DataFrame({
        'claim_id': ['CLMEA1A949D', 'CLMF72F0B6C'],
        'patient_id': ['PATFD81BF08', 'PAT46843087'],
        'provider_id': ['PROVC934AC56', 'PROV131A6DFE']
    })
    
    schema = [('claim_id', 'varchar'), ('patient_id', 'varchar'), ('provider_id', 'varchar')]
    
    print("Testing with force_refresh=True...")
    plan = get_masking_plan(schema, df, use_gpt=False, force_refresh=True)
    
    print("\nResults:")
    for p in plan:
        col = p['column']
        method = p['suggested_masking']
        expected = "format_preserving_id"
        status = "✅" if method == expected else "❌"
        print(f"{col}: {method} {status}")

if __name__ == "__main__":
    main()
